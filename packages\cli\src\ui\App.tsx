/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import { Config } from '@arien/arien-cli-core';
import { ChatInterface } from '../components/ChatInterface.js';
import { StatusBar } from '../components/StatusBar.js';
import { WelcomeScreen } from '../components/WelcomeScreen.js';
import { ErrorBoundary } from '../components/ErrorBoundary.js';
import { ServiceStatus } from '../components/ServiceStatus.js';
import { SettingsScreen } from '../components/SettingsScreen.js';
import { useConfig } from '../hooks/useConfig.js';
import { useChat } from '../hooks/useChat.js';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts.js';

type Screen = 'welcome' | 'chat' | 'settings' | 'help' | 'services';

interface AppState {
  screen: Screen;
  isLoading: boolean;
  error?: string;
}

interface AppProps {
  config: Config;
  settings?: Config; // Optional for backward compatibility
  startupWarnings?: string[];
  initialCommand?: string;
  debug?: boolean;
}

export const AppWrapper = (props: AppProps) => {
  return <App {...props} />;
};

const App = ({
  config: initialConfig,
  settings,
  startupWarnings = [],
  initialCommand,
  debug = false
}: AppProps) => {
  // Suppress unused parameter warnings for backward compatibility
  void settings;
  void startupWarnings;

  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    screen: initialCommand ? 'chat' : 'welcome',
    isLoading: false,
  });

  // Use integrated hooks
  const { config, updateConfig, isConfigValid, configErrors } = useConfig(initialConfig);
  const {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error: chatError,
  } = useChat(config);

  // Keyboard shortcuts integration
  useKeyboardShortcuts({
    onExit: () => exit(),
    onClear: clearChat,
    onToggleHelp: () => setState(prev => ({
      ...prev,
      screen: prev.screen === 'help' ? 'chat' : 'help',
    })),
  });

  // Handle initial command
  useEffect(() => {
    if (initialCommand && state.screen === 'chat' && isConfigValid) {
      sendMessage(initialCommand);
    }
  }, [initialCommand, state.screen, isConfigValid, sendMessage]);

  // Handle configuration validation
  useEffect(() => {
    if (!isConfigValid) {
      setState(prev => ({
        ...prev,
        error: 'Configuration is invalid. Please check your settings.',
      }));
    } else {
      setState(prev => ({ ...prev, error: undefined }));
    }
  }, [isConfigValid]);

  // Handle chat errors
  useEffect(() => {
    if (chatError) {
      setState(prev => ({ ...prev, error: chatError }));
    }
  }, [chatError]);

  const handleScreenChange = useCallback((screen: Screen) => {
    setState(prev => ({ ...prev, screen }));
  }, []);

  const handleStartChat = useCallback(() => {
    if (isConfigValid) {
      handleScreenChange('chat');
    } else {
      handleScreenChange('settings');
    }
  }, [isConfigValid, handleScreenChange]);

  const renderScreen = () => {
    switch (state.screen) {
      case 'welcome':
        return (
          <WelcomeScreen
            config={config}
            onStartChat={handleStartChat}
            onShowSettings={() => handleScreenChange('settings')}
            onShowHelp={() => handleScreenChange('help')}
          />
        );

      case 'chat':
        return (
          <ChatInterface
            messages={messages}
            isGenerating={isGenerating}
            onSendMessage={sendMessage}
            onClear={clearChat}
            config={config}
          />
        );

      case 'settings':
        return (
          <SettingsScreen
            config={config}
            configErrors={configErrors}
            isConfigValid={isConfigValid}
          />
        );

      case 'help':
        return (
          <Box flexDirection="column">
            <Text color="cyan" bold>Arien CLI Help</Text>
            <Text></Text>
            <Text color="white">Keyboard Shortcuts:</Text>
            <Text color="gray"> Ctrl+C / q - Exit</Text>
            <Text color="gray"> Ctrl+L - Clear chat</Text>
            <Text color="gray"> Ctrl+H / ? - Toggle help</Text>
            <Text color="gray"> Ctrl+S - Service status</Text>
            <Text color="gray"> Tab - Autocomplete</Text>
            <Text color="gray"> ↑/↓ - Command history</Text>
            <Text></Text>
            <Text color="white">Commands:</Text>
            <Text color="gray"> /clear - Clear chat history</Text>
            <Text color="gray"> /help - Show this help</Text>
            <Text color="gray"> /quit - Exit application</Text>
            <Text color="gray"> /settings - Open settings</Text>
            <Text color="gray"> /services - Show service status</Text>
            <Text></Text>
            <Text color="gray">Press any key to return to chat</Text>
          </Box>
        );

      case 'services':
        return <ServiceStatus config={config} />;

      default:
        return <Text color="red">Unknown screen: {state.screen}</Text>;
    }
  };

  // Handle input for different screens
  useInput((input, key) => {
    if (state.screen === 'help') {
      handleScreenChange('chat');
      return;
    }

    if (state.screen === 'services') {
      handleScreenChange('chat');
      return;
    }

    if (state.screen === 'settings') {
      if (input === 'q') {
        exit();
      } else if (input === 'c') {
        handleScreenChange('chat');
      }
      return;
    }

    if (state.screen === 'welcome') {
      if (input === 'q') {
        exit();
      } else if (input === 'c' || key.return) {
        handleStartChat();
      } else if (input === 's') {
        handleScreenChange('settings');
      } else if (input === 'v') {
        handleScreenChange('services');
      } else if (input === 'h' || input === '?') {
        handleScreenChange('help');
      }
      return;
    }
  });

  return (
    <ErrorBoundary>
      <Box flexDirection="column" height="100%">
        <StatusBar
          config={config}
          isGenerating={isGenerating}
          error={state.error}
          screen={state.screen}
          showServiceStatus={true}
        />

        <Box flexGrow={1} flexDirection="column">
          {state.isLoading ? (
            <Box justifyContent="center" alignItems="center" flexGrow={1}>
              <Text color="yellow">Loading...</Text>
            </Box>
          ) : (
            renderScreen()
          )}
        </Box>

        {debug && (
          <Box borderStyle="single" borderColor="gray" padding={1}>
            <Text color="gray" dimColor>
              Debug: Screen={state.screen} | Config Valid={isConfigValid} | Messages={messages.length} | Generating={isGenerating}
            </Text>
          </Box>
        )}
      </Box>
    </ErrorBoundary>
  );
};

export default App;
