/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { Config } from '@arien/arien-cli-core';
import { LoadingSpinner } from './LoadingSpinner.js';

interface ServiceStatusProps {
  config: Config;
}

interface ServiceInfo {
  fileService: {
    status: 'active' | 'inactive' | 'error';
    filesDiscovered?: number;
    error?: string;
  };
  gitService: {
    status: 'active' | 'inactive' | 'error';
    isRepository?: boolean;
    branch?: string;
    hasChanges?: boolean;
    error?: string;
  };
  mcpServers: {
    status: 'active' | 'inactive' | 'error';
    activeCount: number;
    totalCount: number;
    error?: string;
  };
  toolRegistry: {
    status: 'active' | 'inactive' | 'error';
    toolCount: number;
    error?: string;
  };
}

export const ServiceStatus: React.FC<ServiceStatusProps> = ({ config }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [serviceInfo, setServiceInfo] = useState<ServiceInfo>({
    fileService: { status: 'inactive' },
    gitService: { status: 'inactive' },
    mcpServers: { status: 'inactive', activeCount: 0, totalCount: 0 },
    toolRegistry: { status: 'inactive', toolCount: 0 },
  });

  useEffect(() => {
    const checkServices = async () => {
      setIsLoading(true);
      const newServiceInfo: ServiceInfo = {
        fileService: { status: 'inactive' },
        gitService: { status: 'inactive' },
        mcpServers: { status: 'inactive', activeCount: 0, totalCount: 0 },
        toolRegistry: { status: 'inactive', toolCount: 0 },
      };

      // Check FileDiscoveryService
      try {
        const fileService = config.getFileService();
        if (fileService) {
          const files = await fileService.discoverFiles({ maxFiles: 10 });
          newServiceInfo.fileService = {
            status: 'active',
            filesDiscovered: files.length,
          };
        }
      } catch (error) {
        newServiceInfo.fileService = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }

      // Check GitService
      try {
        const gitService = config.getGitService();
        if (gitService) {
          const gitStatus = await gitService.getStatus();
          newServiceInfo.gitService = {
            status: 'active',
            isRepository: gitStatus.isRepository,
            branch: gitStatus.branch,
            hasChanges: gitStatus.hasChanges,
          };
        }
      } catch (error) {
        newServiceInfo.gitService = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }

      // Check MCPServerManager
      try {
        const mcpManager = config.getMCPServerManager();
        if (mcpManager) {
          const activeServers = mcpManager.getActiveServers();
          newServiceInfo.mcpServers = {
            status: activeServers.length > 0 ? 'active' : 'inactive',
            activeCount: activeServers.length,
            totalCount: activeServers.length, // For now, assume all configured servers are active
          };
        }
      } catch (error) {
        newServiceInfo.mcpServers = {
          status: 'error',
          activeCount: 0,
          totalCount: 0,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }

      // Check ToolRegistry
      try {
        const toolRegistry = config.getToolRegistry();
        if (toolRegistry) {
          const tools = toolRegistry.getAvailableTools();
          newServiceInfo.toolRegistry = {
            status: 'active',
            toolCount: tools.length,
          };
        }
      } catch (error) {
        newServiceInfo.toolRegistry = {
          status: 'error',
          toolCount: 0,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }

      setServiceInfo(newServiceInfo);
      setIsLoading(false);
    };

    checkServices();
    const interval = setInterval(checkServices, 5000);
    return () => clearInterval(interval);
  }, [config]);

  const getStatusColor = (status: 'active' | 'inactive' | 'error') => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'yellow';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getStatusIcon = (status: 'active' | 'inactive' | 'error') => {
    switch (status) {
      case 'active':
        return '✅';
      case 'inactive':
        return '⏸️';
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  };

  if (isLoading) {
    return (
      <Box flexDirection="column" padding={1} justifyContent="center" alignItems="center" height="100%">
        <LoadingSpinner text="Loading service status" color="cyan" type="dots" />
      </Box>
    );
  }

  return (
    <Box flexDirection="column" paddingX={2} paddingY={1}>
      <Text bold color="cyan">
        🔧 Service Integration Status
      </Text>
      
      <Box flexDirection="column" marginTop={1}>
        {/* File Discovery Service */}
        <Box>
          <Text color={getStatusColor(serviceInfo.fileService.status)}>
            {getStatusIcon(serviceInfo.fileService.status)} File Discovery Service
          </Text>
          {serviceInfo.fileService.status === 'active' && (
            <Text color="gray" dimColor>
              {' '}({serviceInfo.fileService.filesDiscovered} files discovered)
            </Text>
          )}
          {serviceInfo.fileService.error && (
            <Text color="red" dimColor>
              {' '}Error: {serviceInfo.fileService.error}
            </Text>
          )}
        </Box>

        {/* Git Service */}
        <Box>
          <Text color={getStatusColor(serviceInfo.gitService.status)}>
            {getStatusIcon(serviceInfo.gitService.status)} Git Service
          </Text>
          {serviceInfo.gitService.status === 'active' && (
            <Text color="gray" dimColor>
              {serviceInfo.gitService.isRepository 
                ? ` (${serviceInfo.gitService.branch}${serviceInfo.gitService.hasChanges ? ', changes' : ', clean'})`
                : ' (not a git repository)'
              }
            </Text>
          )}
          {serviceInfo.gitService.error && (
            <Text color="red" dimColor>
              {' '}Error: {serviceInfo.gitService.error}
            </Text>
          )}
        </Box>

        {/* MCP Servers */}
        <Box>
          <Text color={getStatusColor(serviceInfo.mcpServers.status)}>
            {getStatusIcon(serviceInfo.mcpServers.status)} MCP Servers
          </Text>
          <Text color="gray" dimColor>
            {' '}({serviceInfo.mcpServers.activeCount}/{serviceInfo.mcpServers.totalCount} active)
          </Text>
          {serviceInfo.mcpServers.error && (
            <Text color="red" dimColor>
              {' '}Error: {serviceInfo.mcpServers.error}
            </Text>
          )}
        </Box>

        {/* Tool Registry */}
        <Box>
          <Text color={getStatusColor(serviceInfo.toolRegistry.status)}>
            {getStatusIcon(serviceInfo.toolRegistry.status)} Tool Registry
          </Text>
          {serviceInfo.toolRegistry.status === 'active' && (
            <Text color="gray" dimColor>
              {' '}({serviceInfo.toolRegistry.toolCount} tools available)
            </Text>
          )}
          {serviceInfo.toolRegistry.error && (
            <Text color="red" dimColor>
              {' '}Error: {serviceInfo.toolRegistry.error}
            </Text>
          )}
        </Box>
      </Box>

      <Box marginTop={1}>
        <Text color="gray" dimColor>
          Press any key to return to chat...
        </Text>
      </Box>
    </Box>
  );
};
