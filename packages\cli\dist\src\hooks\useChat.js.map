{"version": 3, "sources": ["../../../src/hooks/useChat.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport {\n  Config,\n  ContentGenerator,\n  Turn,\n  createContentGenerator,\n  createContentGeneratorConfig,\n  Auth<PERSON>ype,\n  <PERSON>lRegistry,\n  ToolCallRequestInfo,\n  executeToolCall,\n} from '@arien/arien-cli-core';\nimport {\n  Content,\n  Part,\n  FunctionCall,\n  GenerateContentResponse,\n} from '@google/genai';\n\n// Tool approval types (defined locally since they're not exported from core)\nexport interface ToolApprovalRequest {\n  toolName: string;\n  parameters: any;\n  description: string;\n  riskLevel: 'low' | 'medium' | 'high';\n}\n\nexport interface ToolApproval {\n  approved: boolean;\n  reason?: string;\n  rememberChoice?: boolean;\n}\n\n// Helper function to extract text from AI response\nfunction getResponseText(response: GenerateContentResponse): string | null {\n  if (response.candidates && response.candidates.length > 0) {\n    const candidate = response.candidates[0];\n    if (\n      candidate.content &&\n      candidate.content.parts &&\n      candidate.content.parts.length > 0\n    ) {\n      // Filter out thought parts for interactive mode\n      const thoughtPart = candidate.content.parts[0];\n      if (thoughtPart?.thought) {\n        return null;\n      }\n      return candidate.content.parts\n        .filter((part) => part.text)\n        .map((part) => part.text)\n        .join('');\n    }\n  }\n  return null;\n}\n\nexport interface ChatMessage {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  turn?: Turn;\n  error?: string;\n}\n\nexport interface UseChatReturn {\n  messages: ChatMessage[];\n  isGenerating: boolean;\n  sendMessage: (content: string, approvalCallback?: (request: ToolApprovalRequest) => Promise<ToolApproval>) => Promise<void>;\n  clearChat: () => void;\n  error?: string;\n}\n\nexport const useChat = (config: Config): UseChatReturn => {\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n\n  const contentGeneratorRef = useRef<ContentGenerator | null>(null);\n  const turnCounterRef = useRef(0);\n\n  // Initialize content generator\n  useEffect(() => {\n    const initializeGenerator = async () => {\n      try {\n        const configData = config.getContentGeneratorConfig();\n        if (!configData) {\n          throw new Error('No content generator configuration found');\n        }\n\n        // Use the authType directly since it's now using the unified enum values\n        const mappedAuthType: AuthType = configData.authType || AuthType.USE_GEMINI;\n\n        const generatorConfig = await createContentGeneratorConfig(\n          config.getModel(),\n          configData.apiKey,\n          mappedAuthType,\n        );\n\n        const generator = await createContentGenerator(generatorConfig);\n        contentGeneratorRef.current = generator;\n        setError(undefined);\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error\n            ? err.message\n            : 'Failed to initialize content generator';\n        setError(errorMessage);\n        console.error('Failed to initialize content generator:', err);\n      }\n    };\n\n    initializeGenerator();\n  }, [config]);\n\n  const generateId = useCallback(() => {\n    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }, []);\n\n  const sendMessage = useCallback(\n    async (content: string, approvalCallback?: (request: ToolApprovalRequest) => Promise<ToolApproval>) => {\n      if (!content.trim() || isGenerating) {\n        return;\n      }\n\n      if (!contentGeneratorRef.current) {\n        setError('Content generator not initialized');\n        return;\n      }\n\n      // Create user message\n      const userMessage: ChatMessage = {\n        id: generateId(),\n        role: 'user',\n        content: content.trim(),\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev, userMessage]);\n      setIsGenerating(true);\n      setError(undefined);\n\n      try {\n        // Get AI client and tool registry\n        const geminiClient = config.getGeminiClient();\n        const toolRegistry: ToolRegistry = await config.getToolRegistry();\n        const chat = await geminiClient.getChat();\n\n        // Convert messages to Gemini format\n        let currentMessages: Content[] = [{\n          role: 'user',\n          parts: [{ text: content.trim() }]\n        }];\n\n        // Main conversation loop with tool execution\n        while (true) {\n          const functionCalls: FunctionCall[] = [];\n\n          // Send message to AI with tool declarations\n          const responseStream = await chat.sendMessageStream({\n            message: currentMessages[0]?.parts || [],\n            config: {\n              tools: [\n                { functionDeclarations: toolRegistry.getFunctionDeclarations() },\n              ],\n            },\n          });\n\n          let responseText = '';\n\n          // Process streaming response\n          for await (const resp of responseStream) {\n            const textPart = getResponseText(resp);\n            if (textPart) {\n              responseText += textPart;\n            }\n            if (resp.functionCalls) {\n              functionCalls.push(...resp.functionCalls);\n            }\n          }\n\n          // Add AI response to messages if there's text content\n          if (responseText.trim()) {\n            const assistantMessage: ChatMessage = {\n              id: generateId(),\n              role: 'assistant',\n              content: responseText,\n              timestamp: new Date(),\n            };\n            setMessages(prev => [...prev, assistantMessage]);\n          }\n\n          // Handle function calls if any\n          if (functionCalls.length > 0) {\n            const toolResponseParts: Part[] = [];\n\n            for (const fc of functionCalls) {\n              const callId = fc.id ?? `${fc.name}-${Date.now()}`;\n              const requestInfo: ToolCallRequestInfo = {\n                callId,\n                name: fc.name as string,\n                args: fc.args || {},\n              };\n\n              try {\n                let result;\n\n                // Check if tool requires approval and we have an approval callback\n                const tool = await toolRegistry.get(fc.name as string);\n                if (tool && approvalCallback) {\n                  // Check if this is an approval-required tool\n                  const toolDefinition = toolRegistry.getToolDefinition(fc.name as string);\n                  const riskLevel = toolDefinition?.riskLevel || 'low';\n\n                  // Create approval request\n                  const approvalRequest: ToolApprovalRequest = {\n                    toolName: fc.name as string,\n                    parameters: fc.args || {},\n                    description: toolDefinition?.description || `Execute ${fc.name}`,\n                    riskLevel: riskLevel as 'low' | 'medium' | 'high',\n                  };\n\n                  // Request approval\n                  const approval = await approvalCallback(approvalRequest);\n\n                  if (!approval.approved) {\n                    // Tool execution denied\n                    toolResponseParts.push({\n                      functionResponse: {\n                        name: fc.name,\n                        response: {\n                          error: `Tool execution denied: ${approval.reason || 'User declined'}`,\n                        },\n                      },\n                    });\n\n                    // Add denial message to chat\n                    const denialMessage: ChatMessage = {\n                      id: generateId(),\n                      role: 'assistant',\n                      content: `🚫 Tool execution denied: ${fc.name} - ${approval.reason || 'User declined'}`,\n                      timestamp: new Date(),\n                    };\n                    setMessages(prev => [...prev, denialMessage]);\n                    continue;\n                  }\n                }\n\n                // Execute tool (either approved or doesn't require approval)\n                result = await executeToolCall(\n                  requestInfo,\n                  toolRegistry,\n                  config,\n                  undefined, // abortSignal\n                );\n\n                toolResponseParts.push({\n                  functionResponse: {\n                    name: fc.name,\n                    response: result,\n                  },\n                });\n\n                // Add tool execution message to chat\n                const toolMessage: ChatMessage = {\n                  id: generateId(),\n                  role: 'assistant',\n                  content: `🔧 Executed tool: ${fc.name}`,\n                  timestamp: new Date(),\n                };\n                setMessages(prev => [...prev, toolMessage]);\n              } catch (error) {\n                const errorMsg = error instanceof Error ? error.message : String(error);\n                toolResponseParts.push({\n                  functionResponse: {\n                    name: fc.name,\n                    response: {\n                      error: errorMsg,\n                    },\n                  },\n                });\n\n                // Add tool error message to chat\n                const toolErrorMessage: ChatMessage = {\n                  id: generateId(),\n                  role: 'assistant',\n                  content: `❌ Tool error (${fc.name}): ${errorMsg}`,\n                  timestamp: new Date(),\n                  error: errorMsg,\n                };\n                setMessages(prev => [...prev, toolErrorMessage]);\n              }\n            }\n\n            // Continue conversation with tool responses\n            currentMessages = [\n              {\n                role: 'model',\n                parts: functionCalls.map((fc) => ({\n                  functionCall: {\n                    name: fc.name,\n                    args: fc.args || {},\n                  },\n                })),\n              },\n              {\n                role: 'user',\n                parts: toolResponseParts,\n              },\n            ];\n          } else {\n            // No more function calls, conversation is complete\n            break;\n          }\n        }\n      } catch (err) {\n        const errorMessage =\n          err instanceof Error ? err.message : 'Failed to generate response';\n        setError(errorMessage);\n\n        // Add error message to chat\n        const errorChatMessage: ChatMessage = {\n          id: generateId(),\n          role: 'assistant',\n          content: `❌ Error: ${errorMessage}`,\n          timestamp: new Date(),\n          error: errorMessage,\n        };\n\n        setMessages(prev => [...prev, errorChatMessage]);\n      } finally {\n        setIsGenerating(false);\n      }\n    },\n    [config, isGenerating, generateId],\n  );\n\n  const clearChat = useCallback(() => {\n    setMessages([]);\n    setError(undefined);\n    turnCounterRef.current = 0;\n  }, []);\n\n  return {\n    messages,\n    isGenerating,\n    sendMessage,\n    clearChat,\n    error,\n  };\n};\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,UAAU,aAAa,QAAQ,iBAAiB;AACzD;AAAA,EAIE;AAAA,EACA;AAAA,EACA;AAAA,EAGA;AAAA,OACK;AAuBP,SAAS,gBAAgB,UAAkD;AACzE,MAAI,SAAS,cAAc,SAAS,WAAW,SAAS,GAAG;AACzD,UAAM,YAAY,SAAS,WAAW,CAAC;AACvC,QACE,UAAU,WACV,UAAU,QAAQ,SAClB,UAAU,QAAQ,MAAM,SAAS,GACjC;AAEA,YAAM,cAAc,UAAU,QAAQ,MAAM,CAAC;AAC7C,UAAI,aAAa,SAAS;AACxB,eAAO;AAAA,MACT;AACA,aAAO,UAAU,QAAQ,MACtB,OAAO,CAAC,SAAS,KAAK,IAAI,EAC1B,IAAI,CAAC,SAAS,KAAK,IAAI,EACvB,KAAK,EAAE;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AAmBO,MAAM,UAAU,CAAC,WAAkC;AACxD,QAAM,CAAC,UAAU,WAAW,IAAI,SAAwB,CAAC,CAAC;AAC1D,QAAM,CAAC,cAAc,eAAe,IAAI,SAAS,KAAK;AACtD,QAAM,CAAC,OAAO,QAAQ,IAAI,SAA6B;AAEvD,QAAM,sBAAsB,OAAgC,IAAI;AAChE,QAAM,iBAAiB,OAAO,CAAC;AAG/B,YAAU,MAAM;AACd,UAAM,sBAAsB,YAAY;AACtC,UAAI;AACF,cAAM,aAAa,OAAO,0BAA0B;AACpD,YAAI,CAAC,YAAY;AACf,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AAGA,cAAM,iBAA2B,WAAW,YAAY,SAAS;AAEjE,cAAM,kBAAkB,MAAM;AAAA,UAC5B,OAAO,SAAS;AAAA,UAChB,WAAW;AAAA,UACX;AAAA,QACF;AAEA,cAAM,YAAY,MAAM,uBAAuB,eAAe;AAC9D,4BAAoB,UAAU;AAC9B,iBAAS,MAAS;AAAA,MACpB,SAAS,KAAK;AACZ,cAAM,eACJ,eAAe,QACX,IAAI,UACJ;AACN,iBAAS,YAAY;AACrB,gBAAQ,MAAM,2CAA2C,GAAG;AAAA,MAC9D;AAAA,IACF;AAEA,wBAAoB;AAAA,EACtB,GAAG,CAAC,MAAM,CAAC;AAEX,QAAM,aAAa,YAAY,MAAM;AACnC,WAAO,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AAAA,EACzE,GAAG,CAAC,CAAC;AAEL,QAAM,cAAc;AAAA,IAClB,OAAO,SAAiB,qBAA+E;AACrG,UAAI,CAAC,QAAQ,KAAK,KAAK,cAAc;AACnC;AAAA,MACF;AAEA,UAAI,CAAC,oBAAoB,SAAS;AAChC,iBAAS,mCAAmC;AAC5C;AAAA,MACF;AAGA,YAAM,cAA2B;AAAA,QAC/B,IAAI,WAAW;AAAA,QACf,MAAM;AAAA,QACN,SAAS,QAAQ,KAAK;AAAA,QACtB,WAAW,oBAAI,KAAK;AAAA,MACtB;AAEA,kBAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAC1C,sBAAgB,IAAI;AACpB,eAAS,MAAS;AAElB,UAAI;AAEF,cAAM,eAAe,OAAO,gBAAgB;AAC5C,cAAM,eAA6B,MAAM,OAAO,gBAAgB;AAChE,cAAM,OAAO,MAAM,aAAa,QAAQ;AAGxC,YAAI,kBAA6B,CAAC;AAAA,UAChC,MAAM;AAAA,UACN,OAAO,CAAC,EAAE,MAAM,QAAQ,KAAK,EAAE,CAAC;AAAA,QAClC,CAAC;AAGD,eAAO,MAAM;AACX,gBAAM,gBAAgC,CAAC;AAGvC,gBAAM,iBAAiB,MAAM,KAAK,kBAAkB;AAAA,YAClD,SAAS,gBAAgB,CAAC,GAAG,SAAS,CAAC;AAAA,YACvC,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,EAAE,sBAAsB,aAAa,wBAAwB,EAAE;AAAA,cACjE;AAAA,YACF;AAAA,UACF,CAAC;AAED,cAAI,eAAe;AAGnB,2BAAiB,QAAQ,gBAAgB;AACvC,kBAAM,WAAW,gBAAgB,IAAI;AACrC,gBAAI,UAAU;AACZ,8BAAgB;AAAA,YAClB;AACA,gBAAI,KAAK,eAAe;AACtB,4BAAc,KAAK,GAAG,KAAK,aAAa;AAAA,YAC1C;AAAA,UACF;AAGA,cAAI,aAAa,KAAK,GAAG;AACvB,kBAAM,mBAAgC;AAAA,cACpC,IAAI,WAAW;AAAA,cACf,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW,oBAAI,KAAK;AAAA,YACtB;AACA,wBAAY,UAAQ,CAAC,GAAG,MAAM,gBAAgB,CAAC;AAAA,UACjD;AAGA,cAAI,cAAc,SAAS,GAAG;AAC5B,kBAAM,oBAA4B,CAAC;AAEnC,uBAAW,MAAM,eAAe;AAC9B,oBAAM,SAAS,GAAG,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;AAChD,oBAAM,cAAmC;AAAA,gBACvC;AAAA,gBACA,MAAM,GAAG;AAAA,gBACT,MAAM,GAAG,QAAQ,CAAC;AAAA,cACpB;AAEA,kBAAI;AACF,oBAAI;AAGJ,sBAAM,OAAO,MAAM,aAAa,IAAI,GAAG,IAAc;AACrD,oBAAI,QAAQ,kBAAkB;AAE5B,wBAAM,iBAAiB,aAAa,kBAAkB,GAAG,IAAc;AACvE,wBAAM,YAAY,gBAAgB,aAAa;AAG/C,wBAAM,kBAAuC;AAAA,oBAC3C,UAAU,GAAG;AAAA,oBACb,YAAY,GAAG,QAAQ,CAAC;AAAA,oBACxB,aAAa,gBAAgB,eAAe,WAAW,GAAG,IAAI;AAAA,oBAC9D;AAAA,kBACF;AAGA,wBAAM,WAAW,MAAM,iBAAiB,eAAe;AAEvD,sBAAI,CAAC,SAAS,UAAU;AAEtB,sCAAkB,KAAK;AAAA,sBACrB,kBAAkB;AAAA,wBAChB,MAAM,GAAG;AAAA,wBACT,UAAU;AAAA,0BACR,OAAO,0BAA0B,SAAS,UAAU,eAAe;AAAA,wBACrE;AAAA,sBACF;AAAA,oBACF,CAAC;AAGD,0BAAM,gBAA6B;AAAA,sBACjC,IAAI,WAAW;AAAA,sBACf,MAAM;AAAA,sBACN,SAAS,oCAA6B,GAAG,IAAI,MAAM,SAAS,UAAU,eAAe;AAAA,sBACrF,WAAW,oBAAI,KAAK;AAAA,oBACtB;AACA,gCAAY,UAAQ,CAAC,GAAG,MAAM,aAAa,CAAC;AAC5C;AAAA,kBACF;AAAA,gBACF;AAGA,yBAAS,MAAM;AAAA,kBACb;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA;AAAA,gBACF;AAEA,kCAAkB,KAAK;AAAA,kBACrB,kBAAkB;AAAA,oBAChB,MAAM,GAAG;AAAA,oBACT,UAAU;AAAA,kBACZ;AAAA,gBACF,CAAC;AAGD,sBAAM,cAA2B;AAAA,kBAC/B,IAAI,WAAW;AAAA,kBACf,MAAM;AAAA,kBACN,SAAS,4BAAqB,GAAG,IAAI;AAAA,kBACrC,WAAW,oBAAI,KAAK;AAAA,gBACtB;AACA,4BAAY,UAAQ,CAAC,GAAG,MAAM,WAAW,CAAC;AAAA,cAC5C,SAASA,QAAO;AACd,sBAAM,WAAWA,kBAAiB,QAAQA,OAAM,UAAU,OAAOA,MAAK;AACtE,kCAAkB,KAAK;AAAA,kBACrB,kBAAkB;AAAA,oBAChB,MAAM,GAAG;AAAA,oBACT,UAAU;AAAA,sBACR,OAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF,CAAC;AAGD,sBAAM,mBAAgC;AAAA,kBACpC,IAAI,WAAW;AAAA,kBACf,MAAM;AAAA,kBACN,SAAS,sBAAiB,GAAG,IAAI,MAAM,QAAQ;AAAA,kBAC/C,WAAW,oBAAI,KAAK;AAAA,kBACpB,OAAO;AAAA,gBACT;AACA,4BAAY,UAAQ,CAAC,GAAG,MAAM,gBAAgB,CAAC;AAAA,cACjD;AAAA,YACF;AAGA,8BAAkB;AAAA,cAChB;AAAA,gBACE,MAAM;AAAA,gBACN,OAAO,cAAc,IAAI,CAAC,QAAQ;AAAA,kBAChC,cAAc;AAAA,oBACZ,MAAM,GAAG;AAAA,oBACT,MAAM,GAAG,QAAQ,CAAC;AAAA,kBACpB;AAAA,gBACF,EAAE;AAAA,cACJ;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF,OAAO;AAEL;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,KAAK;AACZ,cAAM,eACJ,eAAe,QAAQ,IAAI,UAAU;AACvC,iBAAS,YAAY;AAGrB,cAAM,mBAAgC;AAAA,UACpC,IAAI,WAAW;AAAA,UACf,MAAM;AAAA,UACN,SAAS,iBAAY,YAAY;AAAA,UACjC,WAAW,oBAAI,KAAK;AAAA,UACpB,OAAO;AAAA,QACT;AAEA,oBAAY,UAAQ,CAAC,GAAG,MAAM,gBAAgB,CAAC;AAAA,MACjD,UAAE;AACA,wBAAgB,KAAK;AAAA,MACvB;AAAA,IACF;AAAA,IACA,CAAC,QAAQ,cAAc,UAAU;AAAA,EACnC;AAEA,QAAM,YAAY,YAAY,MAAM;AAClC,gBAAY,CAAC,CAAC;AACd,aAAS,MAAS;AAClB,mBAAe,UAAU;AAAA,EAC3B,GAAG,CAAC,CAAC;AAEL,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["error"]}