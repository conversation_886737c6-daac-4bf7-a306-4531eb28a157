{"version": 3, "sources": ["../../../src/components/ServiceStatus.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Box, Text } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\nimport { LoadingSpinner } from './LoadingSpinner.js';\n\ninterface ServiceStatusProps {\n  config: Config;\n}\n\ninterface ServiceInfo {\n  fileService: {\n    status: 'active' | 'inactive' | 'error';\n    filesDiscovered?: number;\n    error?: string;\n  };\n  gitService: {\n    status: 'active' | 'inactive' | 'error';\n    isRepository?: boolean;\n    branch?: string;\n    hasChanges?: boolean;\n    error?: string;\n  };\n  mcpServers: {\n    status: 'active' | 'inactive' | 'error';\n    activeCount: number;\n    totalCount: number;\n    error?: string;\n  };\n  toolRegistry: {\n    status: 'active' | 'inactive' | 'error';\n    toolCount: number;\n    error?: string;\n  };\n}\n\nexport const ServiceStatus: React.FC<ServiceStatusProps> = ({ config }) => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [serviceInfo, setServiceInfo] = useState<ServiceInfo>({\n    fileService: { status: 'inactive' },\n    gitService: { status: 'inactive' },\n    mcpServers: { status: 'inactive', activeCount: 0, totalCount: 0 },\n    toolRegistry: { status: 'inactive', toolCount: 0 },\n  });\n\n  useEffect(() => {\n    const checkServices = async () => {\n      setIsLoading(true);\n      const newServiceInfo: ServiceInfo = {\n        fileService: { status: 'inactive' },\n        gitService: { status: 'inactive' },\n        mcpServers: { status: 'inactive', activeCount: 0, totalCount: 0 },\n        toolRegistry: { status: 'inactive', toolCount: 0 },\n      };\n\n      // Check FileDiscoveryService\n      try {\n        const fileService = config.getFileService();\n        if (fileService) {\n          const files = await fileService.discoverFiles({ maxFiles: 10 });\n          newServiceInfo.fileService = {\n            status: 'active',\n            filesDiscovered: files.length,\n          };\n        }\n      } catch (error) {\n        newServiceInfo.fileService = {\n          status: 'error',\n          error: error instanceof Error ? error.message : 'Unknown error',\n        };\n      }\n\n      // Check GitService\n      try {\n        const gitService = config.getGitService();\n        if (gitService) {\n          const gitStatus = await gitService.getStatus();\n          newServiceInfo.gitService = {\n            status: 'active',\n            isRepository: gitStatus.isRepository,\n            branch: gitStatus.branch,\n            hasChanges: gitStatus.hasChanges,\n          };\n        }\n      } catch (error) {\n        newServiceInfo.gitService = {\n          status: 'error',\n          error: error instanceof Error ? error.message : 'Unknown error',\n        };\n      }\n\n      // Check MCPServerManager\n      try {\n        const mcpManager = config.getMCPServerManager();\n        if (mcpManager) {\n          const activeServers = mcpManager.getActiveServers();\n          newServiceInfo.mcpServers = {\n            status: activeServers.length > 0 ? 'active' : 'inactive',\n            activeCount: activeServers.length,\n            totalCount: activeServers.length, // For now, assume all configured servers are active\n          };\n        }\n      } catch (error) {\n        newServiceInfo.mcpServers = {\n          status: 'error',\n          activeCount: 0,\n          totalCount: 0,\n          error: error instanceof Error ? error.message : 'Unknown error',\n        };\n      }\n\n      // Check ToolRegistry\n      try {\n        const toolRegistry = config.getToolRegistry();\n        if (toolRegistry) {\n          const tools = toolRegistry.getAvailableTools();\n          newServiceInfo.toolRegistry = {\n            status: 'active',\n            toolCount: tools.length,\n          };\n        }\n      } catch (error) {\n        newServiceInfo.toolRegistry = {\n          status: 'error',\n          toolCount: 0,\n          error: error instanceof Error ? error.message : 'Unknown error',\n        };\n      }\n\n      setServiceInfo(newServiceInfo);\n      setIsLoading(false);\n    };\n\n    checkServices();\n    const interval = setInterval(checkServices, 5000);\n    return () => clearInterval(interval);\n  }, [config]);\n\n  const getStatusColor = (status: 'active' | 'inactive' | 'error') => {\n    switch (status) {\n      case 'active':\n        return 'green';\n      case 'inactive':\n        return 'yellow';\n      case 'error':\n        return 'red';\n      default:\n        return 'gray';\n    }\n  };\n\n  const getStatusIcon = (status: 'active' | 'inactive' | 'error') => {\n    switch (status) {\n      case 'active':\n        return '✅';\n      case 'inactive':\n        return '⏸️';\n      case 'error':\n        return '❌';\n      default:\n        return '❓';\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Box flexDirection=\"column\" padding={1} justifyContent=\"center\" alignItems=\"center\" height=\"100%\">\n        <LoadingSpinner text=\"Loading service status\" color=\"cyan\" type=\"dots\" />\n      </Box>\n    );\n  }\n\n  return (\n    <Box flexDirection=\"column\" paddingX={2} paddingY={1}>\n      <Text bold color=\"cyan\">\n        🔧 Service Integration Status\n      </Text>\n      \n      <Box flexDirection=\"column\" marginTop={1}>\n        {/* File Discovery Service */}\n        <Box>\n          <Text color={getStatusColor(serviceInfo.fileService.status)}>\n            {getStatusIcon(serviceInfo.fileService.status)} File Discovery Service\n          </Text>\n          {serviceInfo.fileService.status === 'active' && (\n            <Text color=\"gray\" dimColor>\n              {' '}({serviceInfo.fileService.filesDiscovered} files discovered)\n            </Text>\n          )}\n          {serviceInfo.fileService.error && (\n            <Text color=\"red\" dimColor>\n              {' '}Error: {serviceInfo.fileService.error}\n            </Text>\n          )}\n        </Box>\n\n        {/* Git Service */}\n        <Box>\n          <Text color={getStatusColor(serviceInfo.gitService.status)}>\n            {getStatusIcon(serviceInfo.gitService.status)} Git Service\n          </Text>\n          {serviceInfo.gitService.status === 'active' && (\n            <Text color=\"gray\" dimColor>\n              {serviceInfo.gitService.isRepository \n                ? ` (${serviceInfo.gitService.branch}${serviceInfo.gitService.hasChanges ? ', changes' : ', clean'})`\n                : ' (not a git repository)'\n              }\n            </Text>\n          )}\n          {serviceInfo.gitService.error && (\n            <Text color=\"red\" dimColor>\n              {' '}Error: {serviceInfo.gitService.error}\n            </Text>\n          )}\n        </Box>\n\n        {/* MCP Servers */}\n        <Box>\n          <Text color={getStatusColor(serviceInfo.mcpServers.status)}>\n            {getStatusIcon(serviceInfo.mcpServers.status)} MCP Servers\n          </Text>\n          <Text color=\"gray\" dimColor>\n            {' '}({serviceInfo.mcpServers.activeCount}/{serviceInfo.mcpServers.totalCount} active)\n          </Text>\n          {serviceInfo.mcpServers.error && (\n            <Text color=\"red\" dimColor>\n              {' '}Error: {serviceInfo.mcpServers.error}\n            </Text>\n          )}\n        </Box>\n\n        {/* Tool Registry */}\n        <Box>\n          <Text color={getStatusColor(serviceInfo.toolRegistry.status)}>\n            {getStatusIcon(serviceInfo.toolRegistry.status)} Tool Registry\n          </Text>\n          {serviceInfo.toolRegistry.status === 'active' && (\n            <Text color=\"gray\" dimColor>\n              {' '}({serviceInfo.toolRegistry.toolCount} tools available)\n            </Text>\n          )}\n          {serviceInfo.toolRegistry.error && (\n            <Text color=\"red\" dimColor>\n              {' '}Error: {serviceInfo.toolRegistry.error}\n            </Text>\n          )}\n        </Box>\n      </Box>\n\n      <Box marginTop={1}>\n        <Text color=\"gray\" dimColor>\n          Press any key to return to chat...\n        </Text>\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AA4KQ,cAcE,YAdF;AA5KR;AAAA;AAAA;AAAA;AAAA;AAMA,SAAgB,UAAU,iBAAiB;AAC3C,SAAS,KAAK,YAAY;AAE1B,SAAS,sBAAsB;AAgCxB,MAAM,gBAA8C,CAAC,EAAE,OAAO,MAAM;AACzE,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,IAAI;AAC/C,QAAM,CAAC,aAAa,cAAc,IAAI,SAAsB;AAAA,IAC1D,aAAa,EAAE,QAAQ,WAAW;AAAA,IAClC,YAAY,EAAE,QAAQ,WAAW;AAAA,IACjC,YAAY,EAAE,QAAQ,YAAY,aAAa,GAAG,YAAY,EAAE;AAAA,IAChE,cAAc,EAAE,QAAQ,YAAY,WAAW,EAAE;AAAA,EACnD,CAAC;AAED,YAAU,MAAM;AACd,UAAM,gBAAgB,YAAY;AAChC,mBAAa,IAAI;AACjB,YAAM,iBAA8B;AAAA,QAClC,aAAa,EAAE,QAAQ,WAAW;AAAA,QAClC,YAAY,EAAE,QAAQ,WAAW;AAAA,QACjC,YAAY,EAAE,QAAQ,YAAY,aAAa,GAAG,YAAY,EAAE;AAAA,QAChE,cAAc,EAAE,QAAQ,YAAY,WAAW,EAAE;AAAA,MACnD;AAGA,UAAI;AACF,cAAM,cAAc,OAAO,eAAe;AAC1C,YAAI,aAAa;AACf,gBAAM,QAAQ,MAAM,YAAY,cAAc,EAAE,UAAU,GAAG,CAAC;AAC9D,yBAAe,cAAc;AAAA,YAC3B,QAAQ;AAAA,YACR,iBAAiB,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,uBAAe,cAAc;AAAA,UAC3B,QAAQ;AAAA,UACR,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,QAClD;AAAA,MACF;AAGA,UAAI;AACF,cAAM,aAAa,OAAO,cAAc;AACxC,YAAI,YAAY;AACd,gBAAM,YAAY,MAAM,WAAW,UAAU;AAC7C,yBAAe,aAAa;AAAA,YAC1B,QAAQ;AAAA,YACR,cAAc,UAAU;AAAA,YACxB,QAAQ,UAAU;AAAA,YAClB,YAAY,UAAU;AAAA,UACxB;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,uBAAe,aAAa;AAAA,UAC1B,QAAQ;AAAA,UACR,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,QAClD;AAAA,MACF;AAGA,UAAI;AACF,cAAM,aAAa,OAAO,oBAAoB;AAC9C,YAAI,YAAY;AACd,gBAAM,gBAAgB,WAAW,iBAAiB;AAClD,yBAAe,aAAa;AAAA,YAC1B,QAAQ,cAAc,SAAS,IAAI,WAAW;AAAA,YAC9C,aAAa,cAAc;AAAA,YAC3B,YAAY,cAAc;AAAA;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,uBAAe,aAAa;AAAA,UAC1B,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,QAClD;AAAA,MACF;AAGA,UAAI;AACF,cAAM,eAAe,OAAO,gBAAgB;AAC5C,YAAI,cAAc;AAChB,gBAAM,QAAQ,aAAa,kBAAkB;AAC7C,yBAAe,eAAe;AAAA,YAC5B,QAAQ;AAAA,YACR,WAAW,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,uBAAe,eAAe;AAAA,UAC5B,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,OAAO,iBAAiB,QAAQ,MAAM,UAAU;AAAA,QAClD;AAAA,MACF;AAEA,qBAAe,cAAc;AAC7B,mBAAa,KAAK;AAAA,IACpB;AAEA,kBAAc;AACd,UAAM,WAAW,YAAY,eAAe,GAAI;AAChD,WAAO,MAAM,cAAc,QAAQ;AAAA,EACrC,GAAG,CAAC,MAAM,CAAC;AAEX,QAAM,iBAAiB,CAAC,WAA4C;AAClE,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAEA,QAAM,gBAAgB,CAAC,WAA4C;AACjE,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAEA,MAAI,WAAW;AACb,WACE,oBAAC,OAAI,eAAc,UAAS,SAAS,GAAG,gBAAe,UAAS,YAAW,UAAS,QAAO,QACzF,8BAAC,kBAAe,MAAK,0BAAyB,OAAM,QAAO,MAAK,QAAO,GACzE;AAAA,EAEJ;AAEA,SACE,qBAAC,OAAI,eAAc,UAAS,UAAU,GAAG,UAAU,GACjD;AAAA,wBAAC,QAAK,MAAI,MAAC,OAAM,QAAO,kDAExB;AAAA,IAEA,qBAAC,OAAI,eAAc,UAAS,WAAW,GAErC;AAAA,2BAAC,OACC;AAAA,6BAAC,QAAK,OAAO,eAAe,YAAY,YAAY,MAAM,GACvD;AAAA,wBAAc,YAAY,YAAY,MAAM;AAAA,UAAE;AAAA,WACjD;AAAA,QACC,YAAY,YAAY,WAAW,YAClC,qBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB;AAAA;AAAA,UAAI;AAAA,UAAE,YAAY,YAAY;AAAA,UAAgB;AAAA,WACjD;AAAA,QAED,YAAY,YAAY,SACvB,qBAAC,QAAK,OAAM,OAAM,UAAQ,MACvB;AAAA;AAAA,UAAI;AAAA,UAAQ,YAAY,YAAY;AAAA,WACvC;AAAA,SAEJ;AAAA,MAGA,qBAAC,OACC;AAAA,6BAAC,QAAK,OAAO,eAAe,YAAY,WAAW,MAAM,GACtD;AAAA,wBAAc,YAAY,WAAW,MAAM;AAAA,UAAE;AAAA,WAChD;AAAA,QACC,YAAY,WAAW,WAAW,YACjC,oBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB,sBAAY,WAAW,eACpB,KAAK,YAAY,WAAW,MAAM,GAAG,YAAY,WAAW,aAAa,cAAc,SAAS,MAChG,2BAEN;AAAA,QAED,YAAY,WAAW,SACtB,qBAAC,QAAK,OAAM,OAAM,UAAQ,MACvB;AAAA;AAAA,UAAI;AAAA,UAAQ,YAAY,WAAW;AAAA,WACtC;AAAA,SAEJ;AAAA,MAGA,qBAAC,OACC;AAAA,6BAAC,QAAK,OAAO,eAAe,YAAY,WAAW,MAAM,GACtD;AAAA,wBAAc,YAAY,WAAW,MAAM;AAAA,UAAE;AAAA,WAChD;AAAA,QACA,qBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB;AAAA;AAAA,UAAI;AAAA,UAAE,YAAY,WAAW;AAAA,UAAY;AAAA,UAAE,YAAY,WAAW;AAAA,UAAW;AAAA,WAChF;AAAA,QACC,YAAY,WAAW,SACtB,qBAAC,QAAK,OAAM,OAAM,UAAQ,MACvB;AAAA;AAAA,UAAI;AAAA,UAAQ,YAAY,WAAW;AAAA,WACtC;AAAA,SAEJ;AAAA,MAGA,qBAAC,OACC;AAAA,6BAAC,QAAK,OAAO,eAAe,YAAY,aAAa,MAAM,GACxD;AAAA,wBAAc,YAAY,aAAa,MAAM;AAAA,UAAE;AAAA,WAClD;AAAA,QACC,YAAY,aAAa,WAAW,YACnC,qBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB;AAAA;AAAA,UAAI;AAAA,UAAE,YAAY,aAAa;AAAA,UAAU;AAAA,WAC5C;AAAA,QAED,YAAY,aAAa,SACxB,qBAAC,QAAK,OAAM,OAAM,UAAQ,MACvB;AAAA;AAAA,UAAI;AAAA,UAAQ,YAAY,aAAa;AAAA,WACxC;AAAA,SAEJ;AAAA,OACF;AAAA,IAEA,oBAAC,OAAI,WAAW,GACd,8BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,gDAE5B,GACF;AAAA,KACF;AAEJ;", "names": []}