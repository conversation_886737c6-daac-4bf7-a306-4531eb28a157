{"version": 3, "sources": ["../../../src/utils/paths.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport path from 'node:path';\nimport os from 'os';\nimport * as crypto from 'crypto';\n\nexport const ARIEN_DIR = '.arien';\nexport const GEMINI_DIR = '.gemini';\nconst TMP_DIR_NAME = 'tmp';\n\n/**\n * Replaces the home directory with a tilde.\n * @param path - The path to tildeify.\n * @returns The tildeified path.\n */\nexport function tildeifyPath(path: string): string {\n  const homeDir = os.homedir();\n  if (path.startsWith(homeDir)) {\n    return path.replace(homeDir, '~');\n  }\n  return path;\n}\n\n/**\n * Shortens a path string if it exceeds maxLen, prioritizing the start and end segments.\n * Example: /path/to/a/very/long/file.txt -> /path/.../long/file.txt\n */\nexport function shortenPath(filePath: string, maxLen: number = 35): string {\n  if (filePath.length <= maxLen) {\n    return filePath;\n  }\n\n  const parsedPath = path.parse(filePath);\n  const root = parsedPath.root;\n  const separator = path.sep;\n\n  // Get segments of the path *after* the root\n  const relativePath = filePath.substring(root.length);\n  const segments = relativePath.split(separator).filter((s) => s !== ''); // Filter out empty segments\n\n  // Handle cases with no segments after root (e.g., \"/\", \"C:\\\") or only one segment\n  if (segments.length <= 1) {\n    // Fallback to simple start/end truncation for very short paths or single segments\n    const keepLen = Math.floor((maxLen - 3) / 2);\n    // Ensure keepLen is not negative if maxLen is very small\n    if (keepLen <= 0) {\n      return filePath.substring(0, maxLen - 3) + '...';\n    }\n    const start = filePath.substring(0, keepLen);\n    const end = filePath.substring(filePath.length - keepLen);\n    return `${start}...${end}`;\n  }\n\n  const firstDir = segments[0];\n  const lastSegment = segments[segments.length - 1];\n  const startComponent = root + firstDir;\n\n  const endPartSegments: string[] = [];\n  // Base length: separator + \"...\" + lastDir\n  let currentLength = separator.length + lastSegment.length;\n\n  // Iterate backwards through segments (excluding the first one)\n  for (let i = segments.length - 2; i >= 0; i--) {\n    const segment = segments[i];\n    // Length needed if we add this segment: current + separator + segment\n    const lengthWithSegment = currentLength + separator.length + segment.length;\n\n    if (lengthWithSegment <= maxLen) {\n      endPartSegments.unshift(segment); // Add to the beginning of the end part\n      currentLength = lengthWithSegment;\n    } else {\n      break;\n    }\n  }\n\n  let result = endPartSegments.join(separator) + separator + lastSegment;\n\n  if (currentLength > maxLen) {\n    return result;\n  }\n\n  // Construct the final path\n  result = startComponent + separator + result;\n\n  // As a final check, if the result is somehow still too long\n  // truncate the result string from the beginning, prefixing with \"...\".\n  if (result.length > maxLen) {\n    return '...' + result.substring(result.length - maxLen - 3);\n  }\n\n  return result;\n}\n\n/**\n * Calculates the relative path from a root directory to a target path.\n * Ensures both paths are resolved before calculating.\n * Returns '.' if the target path is the same as the root directory.\n *\n * @param targetPath The absolute or relative path to make relative.\n * @param rootDirectory The absolute path of the directory to make the target path relative to.\n * @returns The relative path from rootDirectory to targetPath.\n */\nexport function makeRelative(\n  targetPath: string,\n  rootDirectory: string,\n): string {\n  const resolvedTargetPath = path.resolve(targetPath);\n  const resolvedRootDirectory = path.resolve(rootDirectory);\n\n  const relativePath = path.relative(resolvedRootDirectory, resolvedTargetPath);\n\n  // If the paths are the same, path.relative returns '', return '.' instead\n  return relativePath || '.';\n}\n\n/**\n * Escapes spaces in a file path.\n */\nexport function escapePath(filePath: string): string {\n  let result = '';\n  for (let i = 0; i < filePath.length; i++) {\n    // Only escape spaces that are not already escaped.\n    if (filePath[i] === ' ' && (i === 0 || filePath[i - 1] !== '\\\\')) {\n      result += '\\\\ ';\n    } else {\n      result += filePath[i];\n    }\n  }\n  return result;\n}\n\n/**\n * Unescapes spaces in a file path.\n */\nexport function unescapePath(filePath: string): string {\n  return filePath.replace(/\\\\ /g, ' ');\n}\n\n/**\n * Generates a unique hash for a project based on its root path.\n * @param projectRoot The absolute path to the project's root directory.\n * @returns A SHA256 hash of the project root path.\n */\nexport function getProjectHash(projectRoot: string): string {\n  return crypto.createHash('sha256').update(projectRoot).digest('hex');\n}\n\n/**\n * Generates a unique temporary directory path for a project.\n * @param projectRoot The absolute path to the project's root directory.\n * @returns The path to the project's temporary directory.\n */\nexport function getProjectTempDir(projectRoot: string): string {\n  const hash = getProjectHash(projectRoot);\n  return path.join(os.homedir(), ARIEN_DIR, TMP_DIR_NAME, hash);\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,OAAO,UAAU;AACjB,OAAO,QAAQ;AACf,YAAY,YAAY;AAEjB,MAAM,YAAY;AAClB,MAAM,aAAa;AAC1B,MAAM,eAAe;AAOd,SAAS,aAAaA,OAAsB;AACjD,QAAM,UAAU,GAAG,QAAQ;AAC3B,MAAIA,MAAK,WAAW,OAAO,GAAG;AAC5B,WAAOA,MAAK,QAAQ,SAAS,GAAG;AAAA,EAClC;AACA,SAAOA;AACT;AAMO,SAAS,YAAY,UAAkB,SAAiB,IAAY;AACzE,MAAI,SAAS,UAAU,QAAQ;AAC7B,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,KAAK,MAAM,QAAQ;AACtC,QAAM,OAAO,WAAW;AACxB,QAAM,YAAY,KAAK;AAGvB,QAAM,eAAe,SAAS,UAAU,KAAK,MAAM;AACnD,QAAM,WAAW,aAAa,MAAM,SAAS,EAAE,OAAO,CAAC,MAAM,MAAM,EAAE;AAGrE,MAAI,SAAS,UAAU,GAAG;AAExB,UAAM,UAAU,KAAK,OAAO,SAAS,KAAK,CAAC;AAE3C,QAAI,WAAW,GAAG;AAChB,aAAO,SAAS,UAAU,GAAG,SAAS,CAAC,IAAI;AAAA,IAC7C;AACA,UAAM,QAAQ,SAAS,UAAU,GAAG,OAAO;AAC3C,UAAM,MAAM,SAAS,UAAU,SAAS,SAAS,OAAO;AACxD,WAAO,GAAG,KAAK,MAAM,GAAG;AAAA,EAC1B;AAEA,QAAM,WAAW,SAAS,CAAC;AAC3B,QAAM,cAAc,SAAS,SAAS,SAAS,CAAC;AAChD,QAAM,iBAAiB,OAAO;AAE9B,QAAM,kBAA4B,CAAC;AAEnC,MAAI,gBAAgB,UAAU,SAAS,YAAY;AAGnD,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAM,UAAU,SAAS,CAAC;AAE1B,UAAM,oBAAoB,gBAAgB,UAAU,SAAS,QAAQ;AAErE,QAAI,qBAAqB,QAAQ;AAC/B,sBAAgB,QAAQ,OAAO;AAC/B,sBAAgB;AAAA,IAClB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,gBAAgB,KAAK,SAAS,IAAI,YAAY;AAE3D,MAAI,gBAAgB,QAAQ;AAC1B,WAAO;AAAA,EACT;AAGA,WAAS,iBAAiB,YAAY;AAItC,MAAI,OAAO,SAAS,QAAQ;AAC1B,WAAO,QAAQ,OAAO,UAAU,OAAO,SAAS,SAAS,CAAC;AAAA,EAC5D;AAEA,SAAO;AACT;AAWO,SAAS,aACd,YACA,eACQ;AACR,QAAM,qBAAqB,KAAK,QAAQ,UAAU;AAClD,QAAM,wBAAwB,KAAK,QAAQ,aAAa;AAExD,QAAM,eAAe,KAAK,SAAS,uBAAuB,kBAAkB;AAG5E,SAAO,gBAAgB;AACzB;AAKO,SAAS,WAAW,UAA0B;AACnD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAExC,QAAI,SAAS,CAAC,MAAM,QAAQ,MAAM,KAAK,SAAS,IAAI,CAAC,MAAM,OAAO;AAChE,gBAAU;AAAA,IACZ,OAAO;AACL,gBAAU,SAAS,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAKO,SAAS,aAAa,UAA0B;AACrD,SAAO,SAAS,QAAQ,QAAQ,GAAG;AACrC;AAOO,SAAS,eAAe,aAA6B;AAC1D,SAAO,OAAO,WAAW,QAAQ,EAAE,OAAO,WAAW,EAAE,OAAO,KAAK;AACrE;AAOO,SAAS,kBAAkB,aAA6B;AAC7D,QAAM,OAAO,eAAe,WAAW;AACvC,SAAO,KAAK,KAAK,GAAG,QAAQ,GAAG,WAAW,cAAc,IAAI;AAC9D;", "names": ["path"]}