import { jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect, useCallback } from "react";
import { Box, Text, useInput, useApp } from "ink";
import { ChatInterface } from "../components/ChatInterface.js";
import { StatusBar } from "../components/StatusBar.js";
import { WelcomeScreen } from "../components/WelcomeScreen.js";
import { ErrorBoundary } from "../components/ErrorBoundary.js";
import { ServiceStatus } from "../components/ServiceStatus.js";
import { SettingsScreen } from "../components/SettingsScreen.js";
import { useConfig } from "../hooks/useConfig.js";
import { useChat } from "../hooks/useChat.js";
import { useKeyboardShortcuts } from "../hooks/useKeyboardShortcuts.js";
const AppWrapper = (props) => {
  return /* @__PURE__ */ jsx(App, { ...props });
};
const App = ({
  config: initialConfig,
  settings,
  startupWarnings = [],
  initialCommand,
  debug = false
}) => {
  void settings;
  void startupWarnings;
  const { exit } = useApp();
  const [state, setState] = useState({
    screen: initialCommand ? "chat" : "welcome",
    isLoading: false
  });
  const { config, updateConfig, isConfigValid, configErrors } = useConfig(initialConfig);
  const {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error: chatError
  } = useChat(config);
  useKeyboardShortcuts({
    onExit: () => exit(),
    onClear: clearChat,
    onToggleHelp: () => setState((prev) => ({
      ...prev,
      screen: prev.screen === "help" ? "chat" : "help"
    }))
  });
  useEffect(() => {
    if (initialCommand && state.screen === "chat" && isConfigValid) {
      sendMessage(initialCommand);
    }
  }, [initialCommand, state.screen, isConfigValid, sendMessage]);
  useEffect(() => {
    if (!isConfigValid) {
      setState((prev) => ({
        ...prev,
        error: "Configuration is invalid. Please check your settings."
      }));
    } else {
      setState((prev) => ({ ...prev, error: void 0 }));
    }
  }, [isConfigValid]);
  useEffect(() => {
    if (chatError) {
      setState((prev) => ({ ...prev, error: chatError }));
    }
  }, [chatError]);
  const handleScreenChange = useCallback((screen) => {
    setState((prev) => ({ ...prev, screen }));
  }, []);
  const handleStartChat = useCallback(() => {
    if (isConfigValid) {
      handleScreenChange("chat");
    } else {
      handleScreenChange("settings");
    }
  }, [isConfigValid, handleScreenChange]);
  const renderScreen = () => {
    switch (state.screen) {
      case "welcome":
        return /* @__PURE__ */ jsx(
          WelcomeScreen,
          {
            config,
            onStartChat: handleStartChat,
            onShowSettings: () => handleScreenChange("settings"),
            onShowHelp: () => handleScreenChange("help")
          }
        );
      case "chat":
        return /* @__PURE__ */ jsx(
          ChatInterface,
          {
            messages,
            isGenerating,
            onSendMessage: sendMessage,
            onClear: clearChat,
            config
          }
        );
      case "settings":
        return /* @__PURE__ */ jsx(
          SettingsScreen,
          {
            config,
            configErrors,
            isConfigValid
          }
        );
      case "help":
        return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", children: [
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "Arien CLI Help" }),
          /* @__PURE__ */ jsx(Text, {}),
          /* @__PURE__ */ jsx(Text, { color: "white", children: "Keyboard Shortcuts:" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " Ctrl+C / q - Exit" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " Ctrl+L - Clear chat" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " Ctrl+H / ? - Toggle help" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " Ctrl+S - Service status" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " Tab - Autocomplete" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " \u2191/\u2193 - Command history" }),
          /* @__PURE__ */ jsx(Text, {}),
          /* @__PURE__ */ jsx(Text, { color: "white", children: "Commands:" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " /clear - Clear chat history" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " /help - Show this help" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " /quit - Exit application" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " /settings - Open settings" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: " /services - Show service status" }),
          /* @__PURE__ */ jsx(Text, {}),
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Press any key to return to chat" })
        ] });
      case "services":
        return /* @__PURE__ */ jsx(ServiceStatus, { config });
      default:
        return /* @__PURE__ */ jsxs(Text, { color: "red", children: [
          "Unknown screen: ",
          state.screen
        ] });
    }
  };
  useInput((input, key) => {
    if (state.screen === "help") {
      handleScreenChange("chat");
      return;
    }
    if (state.screen === "services") {
      handleScreenChange("chat");
      return;
    }
    if (state.screen === "settings") {
      if (input === "q") {
        exit();
      } else if (input === "c") {
        handleScreenChange("chat");
      }
      return;
    }
    if (state.screen === "welcome") {
      if (input === "q") {
        exit();
      } else if (input === "c" || key.return) {
        handleStartChat();
      } else if (input === "s") {
        handleScreenChange("settings");
      } else if (input === "v") {
        handleScreenChange("services");
      } else if (input === "h" || input === "?") {
        handleScreenChange("help");
      }
      return;
    }
  });
  return /* @__PURE__ */ jsx(ErrorBoundary, { children: /* @__PURE__ */ jsxs(Box, { flexDirection: "column", height: "100%", children: [
    /* @__PURE__ */ jsx(
      StatusBar,
      {
        config,
        isGenerating,
        error: state.error,
        screen: state.screen,
        showServiceStatus: true
      }
    ),
    /* @__PURE__ */ jsx(Box, { flexGrow: 1, flexDirection: "column", children: state.isLoading ? /* @__PURE__ */ jsx(Box, { justifyContent: "center", alignItems: "center", flexGrow: 1, children: /* @__PURE__ */ jsx(Text, { color: "yellow", children: "Loading..." }) }) : renderScreen() }),
    debug && /* @__PURE__ */ jsx(Box, { borderStyle: "single", borderColor: "gray", padding: 1, children: /* @__PURE__ */ jsxs(Text, { color: "gray", dimColor: true, children: [
      "Debug: Screen=",
      state.screen,
      " | Config Valid=",
      isConfigValid,
      " | Messages=",
      messages.length,
      " | Generating=",
      isGenerating
    ] }) })
  ] }) });
};
var App_default = App;
export {
  AppWrapper,
  App_default as default
};
//# sourceMappingURL=App.js.map
