import { jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from "react";
import { Box, Text } from "ink";
import { LoadingSpinner } from "./LoadingSpinner.js";
const ServiceStatus = ({ config }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [serviceInfo, setServiceInfo] = useState({
    fileService: { status: "inactive" },
    gitService: { status: "inactive" },
    mcpServers: { status: "inactive", activeCount: 0, totalCount: 0 },
    toolRegistry: { status: "inactive", toolCount: 0 }
  });
  useEffect(() => {
    const checkServices = async () => {
      setIsLoading(true);
      const newServiceInfo = {
        fileService: { status: "inactive" },
        gitService: { status: "inactive" },
        mcpServers: { status: "inactive", activeCount: 0, totalCount: 0 },
        toolRegistry: { status: "inactive", toolCount: 0 }
      };
      try {
        const fileService = config.getFileService();
        if (fileService) {
          const files = await fileService.discoverFiles({ maxFiles: 10 });
          newServiceInfo.fileService = {
            status: "active",
            filesDiscovered: files.length
          };
        }
      } catch (error) {
        newServiceInfo.fileService = {
          status: "error",
          error: error instanceof Error ? error.message : "Unknown error"
        };
      }
      try {
        const gitService = config.getGitService();
        if (gitService) {
          const gitStatus = await gitService.getStatus();
          newServiceInfo.gitService = {
            status: "active",
            isRepository: gitStatus.isRepository,
            branch: gitStatus.branch,
            hasChanges: gitStatus.hasChanges
          };
        }
      } catch (error) {
        newServiceInfo.gitService = {
          status: "error",
          error: error instanceof Error ? error.message : "Unknown error"
        };
      }
      try {
        const mcpManager = config.getMCPServerManager();
        if (mcpManager) {
          const activeServers = mcpManager.getActiveServers();
          newServiceInfo.mcpServers = {
            status: activeServers.length > 0 ? "active" : "inactive",
            activeCount: activeServers.length,
            totalCount: activeServers.length
            // For now, assume all configured servers are active
          };
        }
      } catch (error) {
        newServiceInfo.mcpServers = {
          status: "error",
          activeCount: 0,
          totalCount: 0,
          error: error instanceof Error ? error.message : "Unknown error"
        };
      }
      try {
        const toolRegistry = config.getToolRegistry();
        if (toolRegistry) {
          const tools = toolRegistry.getAvailableTools();
          newServiceInfo.toolRegistry = {
            status: "active",
            toolCount: tools.length
          };
        }
      } catch (error) {
        newServiceInfo.toolRegistry = {
          status: "error",
          toolCount: 0,
          error: error instanceof Error ? error.message : "Unknown error"
        };
      }
      setServiceInfo(newServiceInfo);
      setIsLoading(false);
    };
    checkServices();
    const interval = setInterval(checkServices, 5e3);
    return () => clearInterval(interval);
  }, [config]);
  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "yellow";
      case "error":
        return "red";
      default:
        return "gray";
    }
  };
  const getStatusIcon = (status) => {
    switch (status) {
      case "active":
        return "\u2705";
      case "inactive":
        return "\u23F8\uFE0F";
      case "error":
        return "\u274C";
      default:
        return "\u2753";
    }
  };
  if (isLoading) {
    return /* @__PURE__ */ jsx(Box, { flexDirection: "column", padding: 1, justifyContent: "center", alignItems: "center", height: "100%", children: /* @__PURE__ */ jsx(LoadingSpinner, { text: "Loading service status", color: "cyan", type: "dots" }) });
  }
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", paddingX: 2, paddingY: 1, children: [
    /* @__PURE__ */ jsx(Text, { bold: true, color: "cyan", children: "\u{1F527} Service Integration Status" }),
    /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 1, children: [
      /* @__PURE__ */ jsxs(Box, { children: [
        /* @__PURE__ */ jsxs(Text, { color: getStatusColor(serviceInfo.fileService.status), children: [
          getStatusIcon(serviceInfo.fileService.status),
          " File Discovery Service"
        ] }),
        serviceInfo.fileService.status === "active" && /* @__PURE__ */ jsxs(Text, { color: "gray", dimColor: true, children: [
          " ",
          "(",
          serviceInfo.fileService.filesDiscovered,
          " files discovered)"
        ] }),
        serviceInfo.fileService.error && /* @__PURE__ */ jsxs(Text, { color: "red", dimColor: true, children: [
          " ",
          "Error: ",
          serviceInfo.fileService.error
        ] })
      ] }),
      /* @__PURE__ */ jsxs(Box, { children: [
        /* @__PURE__ */ jsxs(Text, { color: getStatusColor(serviceInfo.gitService.status), children: [
          getStatusIcon(serviceInfo.gitService.status),
          " Git Service"
        ] }),
        serviceInfo.gitService.status === "active" && /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: serviceInfo.gitService.isRepository ? ` (${serviceInfo.gitService.branch}${serviceInfo.gitService.hasChanges ? ", changes" : ", clean"})` : " (not a git repository)" }),
        serviceInfo.gitService.error && /* @__PURE__ */ jsxs(Text, { color: "red", dimColor: true, children: [
          " ",
          "Error: ",
          serviceInfo.gitService.error
        ] })
      ] }),
      /* @__PURE__ */ jsxs(Box, { children: [
        /* @__PURE__ */ jsxs(Text, { color: getStatusColor(serviceInfo.mcpServers.status), children: [
          getStatusIcon(serviceInfo.mcpServers.status),
          " MCP Servers"
        ] }),
        /* @__PURE__ */ jsxs(Text, { color: "gray", dimColor: true, children: [
          " ",
          "(",
          serviceInfo.mcpServers.activeCount,
          "/",
          serviceInfo.mcpServers.totalCount,
          " active)"
        ] }),
        serviceInfo.mcpServers.error && /* @__PURE__ */ jsxs(Text, { color: "red", dimColor: true, children: [
          " ",
          "Error: ",
          serviceInfo.mcpServers.error
        ] })
      ] }),
      /* @__PURE__ */ jsxs(Box, { children: [
        /* @__PURE__ */ jsxs(Text, { color: getStatusColor(serviceInfo.toolRegistry.status), children: [
          getStatusIcon(serviceInfo.toolRegistry.status),
          " Tool Registry"
        ] }),
        serviceInfo.toolRegistry.status === "active" && /* @__PURE__ */ jsxs(Text, { color: "gray", dimColor: true, children: [
          " ",
          "(",
          serviceInfo.toolRegistry.toolCount,
          " tools available)"
        ] }),
        serviceInfo.toolRegistry.error && /* @__PURE__ */ jsxs(Text, { color: "red", dimColor: true, children: [
          " ",
          "Error: ",
          serviceInfo.toolRegistry.error
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsx(Box, { marginTop: 1, children: /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "Press any key to return to chat..." }) })
  ] });
};
export {
  ServiceStatus
};
//# sourceMappingURL=ServiceStatus.js.map
