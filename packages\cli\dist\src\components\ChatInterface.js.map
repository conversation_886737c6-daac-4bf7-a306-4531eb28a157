{"version": 3, "sources": ["../../../src/components/ChatInterface.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { useState, useCallback } from 'react';\nimport { Box } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\nimport { ChatMessage } from '../hooks/useChat.js';\nimport { MessageList } from './MessageList.js';\nimport { InputBox } from './InputBox.js';\nimport { LoadingSpinner } from './LoadingSpinner.js';\nimport { ToolApproval } from './ToolApproval.js';\nimport { useToolExecution } from '../hooks/useToolExecution.js';\n\ninterface ChatInterfaceProps {\n  messages: ChatMessage[];\n  isGenerating: boolean;\n  onSendMessage: (message: string, approvalCallback?: (request: any) => Promise<any>) => Promise<void>;\n  onClear: () => void;\n  config: Config;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({\n  messages,\n  isGenerating,\n  onSendMessage,\n  onClear,\n  config,\n}) => {\n  const [inputValue, setInputValue] = useState('');\n\n  // Tool execution integration\n  const {\n    pendingApproval,\n    approveTool,\n    denyTool,\n    approveAllTools,\n    denyAllTools,\n    getApprovalCallback,\n  } = useToolExecution(config);\n\n  const handleSubmit = useCallback(\n    async (message: string) => {\n      if (!message.trim() || isGenerating) {\n        return;\n      }\n\n      // Handle built-in commands\n      const trimmedMessage = message.trim();\n\n      if (trimmedMessage === '/clear') {\n        onClear();\n        return;\n      }\n\n      if (trimmedMessage === '/help') {\n        // Add help message to chat\n        const helpMessage = `\nAvailable commands:\n- /clear - Clear chat history\n- /help - Show this help message\n- /quit - Exit the application\n\nKeyboard shortcuts:\n- Ctrl+C - Exit\n- Ctrl+L - Clear chat\n- Ctrl+H - Toggle help\n- Tab - Autocomplete (when available)\n- ↑/↓ - Navigate command history\n      `.trim();\n\n        // This would ideally be handled by adding a system message\n        // For now, we'll just send it as a regular message\n        await onSendMessage(`Help:\\n${helpMessage}`, getApprovalCallback());\n        return;\n      }\n\n      if (trimmedMessage === '/quit') {\n        process.exit(0);\n      }\n\n      // Send the message with tool approval callback\n      await onSendMessage(message, getApprovalCallback());\n      setInputValue('');\n    },\n    [isGenerating, onSendMessage, onClear, getApprovalCallback],\n  );\n\n  const handleInputChange = useCallback((value: string) => {\n    setInputValue(value);\n  }, []);\n\n  // Show tool approval screen if pending\n  if (pendingApproval) {\n    return (\n      <ToolApproval\n        request={pendingApproval}\n        onApprove={approveTool}\n        onDeny={denyTool}\n        onApproveAll={approveAllTools}\n        onDenyAll={denyAllTools}\n      />\n    );\n  }\n\n  return (\n    <Box flexDirection=\"column\" height=\"100%\">\n      {/* Messages Area */}\n      <Box flexGrow={1} flexDirection=\"column\" minHeight={0}>\n        <MessageList messages={messages} />\n\n        {/* Loading indicator */}\n        {isGenerating && (\n          <Box paddingX={2} paddingY={1}>\n            <LoadingSpinner\n              text=\"Generating response\"\n              color=\"yellow\"\n              type=\"dots\"\n            />\n          </Box>\n        )}\n      </Box>\n\n      {/* Input Area */}\n      <Box flexShrink={0}>\n        <InputBox\n          value={inputValue}\n          onChange={handleInputChange}\n          onSubmit={handleSubmit}\n          disabled={isGenerating}\n          placeholder={\n            isGenerating ? 'Generating response...' : 'Type your message...'\n          }\n        />\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AAiGM,cAaA,YAbA;AAjGN;AAAA;AAAA;AAAA;AAAA;AAMA,SAAgB,UAAU,mBAAmB;AAC7C,SAAS,WAAW;AAGpB,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AACzB,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,wBAAwB;AAU1B,MAAM,gBAA8C,CAAC;AAAA,EAC1D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,EAAE;AAG/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,MAAM;AAE3B,QAAM,eAAe;AAAA,IACnB,OAAO,YAAoB;AACzB,UAAI,CAAC,QAAQ,KAAK,KAAK,cAAc;AACnC;AAAA,MACF;AAGA,YAAM,iBAAiB,QAAQ,KAAK;AAEpC,UAAI,mBAAmB,UAAU;AAC/B,gBAAQ;AACR;AAAA,MACF;AAEA,UAAI,mBAAmB,SAAS;AAE9B,cAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYpB,KAAK;AAIL,cAAM,cAAc;AAAA,EAAU,WAAW,IAAI,oBAAoB,CAAC;AAClE;AAAA,MACF;AAEA,UAAI,mBAAmB,SAAS;AAC9B,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAGA,YAAM,cAAc,SAAS,oBAAoB,CAAC;AAClD,oBAAc,EAAE;AAAA,IAClB;AAAA,IACA,CAAC,cAAc,eAAe,SAAS,mBAAmB;AAAA,EAC5D;AAEA,QAAM,oBAAoB,YAAY,CAAC,UAAkB;AACvD,kBAAc,KAAK;AAAA,EACrB,GAAG,CAAC,CAAC;AAGL,MAAI,iBAAiB;AACnB,WACE;AAAA,MAAC;AAAA;AAAA,QACC,SAAS;AAAA,QACT,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,WAAW;AAAA;AAAA,IACb;AAAA,EAEJ;AAEA,SACE,qBAAC,OAAI,eAAc,UAAS,QAAO,QAEjC;AAAA,yBAAC,OAAI,UAAU,GAAG,eAAc,UAAS,WAAW,GAClD;AAAA,0BAAC,eAAY,UAAoB;AAAA,MAGhC,gBACC,oBAAC,OAAI,UAAU,GAAG,UAAU,GAC1B;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,OAAM;AAAA,UACN,MAAK;AAAA;AAAA,MACP,GACF;AAAA,OAEJ;AAAA,IAGA,oBAAC,OAAI,YAAY,GACf;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aACE,eAAe,2BAA2B;AAAA;AAAA,IAE9C,GACF;AAAA,KACF;AAEJ;", "names": []}