/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import {
  GoogleGenAI
} from "@google/genai";
import { DEFAULT_GEMINI_MODEL } from "../config/models.js";
import { AuthType } from "../config/config.js";
async function createContentGeneratorConfig(model, apiKey, authType) {
  const contentGeneratorConfig = {
    model: model || DEFAULT_GEMINI_MODEL,
    apiKey,
    authType: authType || AuthType.USE_GEMINI
  };
  return contentGeneratorConfig;
}
async function createContentGenerator(config, sessionId) {
  const version = process.env.CLI_VERSION || process.version;
  const httpOptions = {
    headers: {
      "User-Agent": `ArienCLI/${version} (${process.platform}; ${process.arch})`
    }
  };
  if (config.authType === AuthType.USE_GEMINI || config.authType === AuthType.USE_VERTEX_AI) {
    const googleGenAI = new GoogleGenAI({
      apiKey: config.apiKey === "" ? void 0 : config.apiKey,
      vertexai: config.vertexai,
      httpOptions
    });
    return googleGenAI.models;
  }
  throw new Error(`Unsupported auth type: ${config.authType}`);
}
export {
  createContentGenerator,
  createContentGeneratorConfig
};
//# sourceMappingURL=contentGenerator.js.map
