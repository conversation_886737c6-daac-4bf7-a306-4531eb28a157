/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { Config, MCPServerConfig } from '../config/config.js';
import { logger } from '../core/logger.js';
import { ToolRegistry } from '../tools/tool-registry.js';
import { MCPClientTool } from '../tools/mcp-client.js';
import { MCPToolFactory } from '../tools/mcp-tool.js';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { getErrorMessage } from '../utils/errors.js';

interface ManagedMCPServer {
  name: string;
  config: MCPServerConfig;
  client?: Client;
  transport?: StdioClientTransport;
  connected: boolean;
  tools: string[];
  lastError?: string;
  retryCount: number;
}

/**
 * Service for managing MCP server lifecycle and automatic startup
 */
export class MCPServerManager {
  private config: Config;
  private toolRegistry: ToolRegistry;
  private servers: Map<string, ManagedMCPServer> = new Map();
  private mcpToolFactory: MCPToolFactory;
  private initialized: boolean = false;

  constructor(config: Config, toolRegistry: ToolRegistry) {
    this.config = config;
    this.toolRegistry = toolRegistry;
    this.mcpToolFactory = new MCPToolFactory(this.createToolCallHandler());
  }

  /**
   * Initialize MCP servers based on configuration
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    logger.debug('Initializing MCP Server Manager');

    const mcpServers = this.config.getMcpServers();
    const serverNames = Object.keys(mcpServers);

    if (serverNames.length === 0) {
      logger.debug('No MCP servers configured');
      this.initialized = true;
      return;
    }

    logger.info(`Starting ${serverNames.length} MCP servers: ${serverNames.join(', ')}`);

    // Start all configured servers
    const startupPromises = serverNames.map(async (serverName) => {
      const serverConfig = mcpServers[serverName];
      try {
        await this.startServer(serverName, serverConfig);
        logger.info(`Successfully started MCP server: ${serverName}`);
      } catch (error) {
        logger.error(`Failed to start MCP server ${serverName}:`, error);
      }
    });

    // Wait for all servers to attempt startup
    await Promise.allSettled(startupPromises);

    this.initialized = true;
    logger.debug('MCP Server Manager initialization complete');
  }

  /**
   * Start a single MCP server
   */
  private async startServer(name: string, config: MCPServerConfig): Promise<void> {
    const server: ManagedMCPServer = {
      name,
      config,
      connected: false,
      tools: [],
      retryCount: 0,
    };

    this.servers.set(name, server);

    try {
      // Create transport and client
      const transport = new StdioClientTransport({
        command: config.command,
        args: config.args || [],
        env: this.createServerEnvironment(config.env || {}),
      });

      const client = new Client(
        {
          name: 'arien-cli',
          version: '0.1.0',
        },
        {
          capabilities: {
            tools: {},
            resources: {},
            prompts: {},
          },
        },
      );

      // Connect to the server
      await client.connect(transport);

      // List available tools
      const toolsResponse = await client.listTools();
      const toolNames = toolsResponse.tools.map(tool => tool.name);

      // Update server state
      server.client = client;
      server.transport = transport;
      server.connected = true;
      server.tools = toolNames;

      // Register MCP tools with the tool registry
      await this.registerServerTools(name, toolsResponse.tools);

      logger.debug(`MCP server ${name} connected with ${toolNames.length} tools: ${toolNames.join(', ')}`);
    } catch (error) {
      server.lastError = getErrorMessage(error);
      server.connected = false;
      throw error;
    }
  }

  /**
   * Register tools from an MCP server with the tool registry
   */
  private async registerServerTools(serverName: string, toolDefinitions: any[]): Promise<void> {
    for (const toolDef of toolDefinitions) {
      try {
        const mcpTool = this.mcpToolFactory.createTool(serverName, toolDef.name, toolDef);
        this.toolRegistry.registerMCPTool(mcpTool);
        logger.debug(`Registered MCP tool: ${mcpTool.name} from server: ${serverName}`);
      } catch (error) {
        logger.error(`Failed to register MCP tool ${toolDef.name} from server ${serverName}:`, error);
      }
    }
  }

  /**
   * Create environment for MCP server process
   */
  private createServerEnvironment(serverEnv: Record<string, string>): Record<string, string> {
    const cleanEnv: Record<string, string> = {};
    
    // Start with process environment, filtering out undefined values
    for (const [key, value] of Object.entries(process.env)) {
      if (value !== undefined) {
        cleanEnv[key] = value;
      }
    }

    // Add server-specific environment variables
    for (const [key, value] of Object.entries(serverEnv)) {
      cleanEnv[key] = value;
    }

    return cleanEnv;
  }

  /**
   * Create tool call handler for MCP tools
   */
  private createToolCallHandler() {
    return async (serverName: string, toolName: string, params: any): Promise<any> => {
      const server = this.servers.get(serverName);
      if (!server) {
        throw new Error(`MCP server not found: ${serverName}`);
      }

      if (!server.connected || !server.client) {
        throw new Error(`MCP server not connected: ${serverName}`);
      }

      try {
        const result = await server.client.callTool({
          name: toolName,
          arguments: params || {},
        });

        return result.content;
      } catch (error) {
        throw new Error(`MCP tool call failed: ${getErrorMessage(error)}`);
      }
    };
  }

  /**
   * Stop a specific MCP server
   */
  async stopServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      return;
    }

    try {
      // Unregister tools from the tool registry
      this.toolRegistry.unregisterMCPServerTools(serverName);

      // Close transport connection
      if (server.transport) {
        await server.transport.close();
      }

      server.connected = false;
      server.client = undefined;
      server.transport = undefined;

      logger.debug(`Stopped MCP server: ${serverName}`);
    } catch (error) {
      logger.error(`Error stopping MCP server ${serverName}:`, error);
      server.lastError = getErrorMessage(error);
    }
  }

  /**
   * Stop all MCP servers
   */
  async stopAllServers(): Promise<void> {
    const serverNames = Array.from(this.servers.keys());
    const stopPromises = serverNames.map(name => this.stopServer(name));
    await Promise.allSettled(stopPromises);
    logger.debug('Stopped all MCP servers');
  }

  /**
   * Get status of all managed servers
   */
  getServerStatus(): Record<string, { connected: boolean; tools: string[]; lastError?: string }> {
    const status: Record<string, { connected: boolean; tools: string[]; lastError?: string }> = {};
    
    for (const [name, server] of this.servers) {
      status[name] = {
        connected: server.connected,
        tools: server.tools,
        lastError: server.lastError,
      };
    }

    return status;
  }

  /**
   * Check if the manager is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get connected server count
   */
  getConnectedServerCount(): number {
    return Array.from(this.servers.values()).filter(server => server.connected).length;
  }
}
