{"version": 3, "sources": ["../../../src/tools/mcp-client.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport {\n  Tool,\n  ToolDefinition,\n  ToolResult,\n  ToolExecutionContext,\n} from './tools.js';\nimport { getErrorMessage } from '../utils/errors.js';\nimport { Client } from '@modelcontextprotocol/sdk/client/index.js';\nimport { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';\n\ninterface MCPClientToolParams {\n  action: 'list' | 'connect' | 'disconnect' | 'call';\n  server_name?: string;\n  server_command?: string;\n  tool_name?: string;\n  tool_params?: Record<string, any>;\n}\n\ninterface MCPServer {\n  name: string;\n  command: string;\n  args?: string[];\n  env?: Record<string, string>;\n  connected: boolean;\n  tools: string[];\n  lastError?: string;\n  client?: Client;\n  transport?: StdioClientTransport;\n}\n\nexport class MCPClientTool extends Tool<MCPClientToolParams> {\n  static readonly Name = 'mcp-client';\n  private servers: Map<string, MCPServer> = new Map();\n\n  constructor() {\n    super(\n      MCPClientTool.Name,\n      'Manage Model Context Protocol (MCP) server connections and tool calls',\n    );\n  }\n\n  getDefinition(): ToolDefinition {\n    return {\n      name: this.name,\n      description: this.description,\n      inputSchema: {\n        type: 'object',\n        properties: {\n          action: {\n            type: 'string',\n            enum: ['list', 'connect', 'disconnect', 'call'],\n            description: 'The MCP action to perform',\n          },\n          server_name: {\n            type: 'string',\n            description:\n              'Name of the MCP server (required for connect, disconnect, call)',\n          },\n          server_command: {\n            type: 'string',\n            description:\n              'Command to start the MCP server (required for connect)',\n          },\n          tool_name: {\n            type: 'string',\n            description: 'Name of the tool to call (required for call action)',\n          },\n          tool_params: {\n            type: 'object',\n            description: 'Parameters to pass to the tool (for call action)',\n            additionalProperties: true,\n          },\n        },\n        required: ['action'],\n      },\n    };\n  }\n\n  private async connectToServer(\n    name: string,\n    command: string,\n    args: string[] = [],\n    env: Record<string, string> = {},\n  ): Promise<MCPServer> {\n    try {\n      // Parse command and arguments\n      const commandParts = command.split(' ');\n      const executable = commandParts[0];\n      const commandArgs = [...commandParts.slice(1), ...args];\n\n      // Create MCP client and transport - let the transport handle process spawning\n      // Filter out undefined environment variables\n      const cleanEnv: Record<string, string> = {};\n      for (const [key, value] of Object.entries({ ...process.env, ...env })) {\n        if (value !== undefined) {\n          cleanEnv[key] = value;\n        }\n      }\n\n      const transport = new StdioClientTransport({\n        command: executable,\n        args: commandArgs,\n        env: cleanEnv,\n      });\n\n      const client = new Client(\n        {\n          name: 'arien-cli',\n          version: '0.1.0',\n        },\n        {\n          capabilities: {\n            tools: {},\n            resources: {},\n            prompts: {},\n          },\n        },\n      );\n\n      // Connect to the server\n      await client.connect(transport);\n\n      // List available tools\n      const toolsResponse = await client.listTools();\n      const toolNames = toolsResponse.tools.map(tool => tool.name);\n\n      const server: MCPServer = {\n        name,\n        command,\n        args,\n        env,\n        connected: true,\n        tools: toolNames,\n        client,\n        transport,\n      };\n\n      this.servers.set(name, server);\n      return server;\n    } catch (error) {\n      const server: MCPServer = {\n        name,\n        command,\n        args,\n        env,\n        connected: false,\n        tools: [],\n        lastError: getErrorMessage(error),\n      };\n\n      this.servers.set(name, server);\n      throw error;\n    }\n  }\n\n  private async disconnectFromServer(name: string): Promise<void> {\n    const server = this.servers.get(name);\n    if (server) {\n      try {\n        // Close the transport connection\n        if (server.transport) {\n          await server.transport.close();\n        }\n      } catch (error) {\n        // Log error but continue with cleanup\n        server.lastError = getErrorMessage(error);\n      }\n\n      server.connected = false;\n      server.tools = [];\n      server.client = undefined;\n      server.transport = undefined;\n    }\n  }\n\n  private async callServerTool(\n    serverName: string,\n    toolName: string,\n    params: Record<string, any>,\n  ): Promise<any> {\n    const server = this.servers.get(serverName);\n    if (!server) {\n      throw new Error(`Server not found: ${serverName}`);\n    }\n\n    if (!server.connected || !server.client) {\n      throw new Error(`Server not connected: ${serverName}`);\n    }\n\n    if (!server.tools.includes(toolName)) {\n      throw new Error(`Tool not available: ${toolName}`);\n    }\n\n    try {\n      // Call the actual MCP server tool\n      const result = await server.client.callTool({\n        name: toolName,\n        arguments: params || {},\n      });\n\n      return {\n        result: result.content,\n        toolName,\n        serverName,\n        timestamp: new Date().toISOString(),\n      };\n    } catch (error) {\n      throw new Error(`Tool call failed: ${getErrorMessage(error)}`);\n    }\n  }\n\n  private formatServerList(): string {\n    if (this.servers.size === 0) {\n      return 'No MCP servers configured.';\n    }\n\n    let output = `MCP Servers (${this.servers.size}):\\n\\n`;\n\n    for (const [name, server] of this.servers) {\n      const status = server.connected ? '🟢 Connected' : '🔴 Disconnected';\n      output += `📡 ${name}\\n`;\n      output += `   Status: ${status}\\n`;\n      output += `   Command: ${server.command}\\n`;\n      output += `   Tools: ${server.tools.length > 0 ? server.tools.join(', ') : 'None'}\\n`;\n\n      if (server.lastError) {\n        output += `   Last Error: ${server.lastError}\\n`;\n      }\n\n      output += '\\n';\n    }\n\n    return output.trim();\n  }\n\n  async execute(\n    params: MCPClientToolParams,\n    context: ToolExecutionContext,\n  ): Promise<ToolResult> {\n    try {\n      switch (params.action) {\n        case 'list': {\n          const output = this.formatServerList();\n          return this.createSuccessResult(output, {\n            serverCount: this.servers.size,\n            connectedServers: Array.from(this.servers.values()).filter(\n              s => s.connected,\n            ).length,\n          });\n        }\n\n        case 'connect': {\n          if (!params.server_name) {\n            return this.createErrorResult(\n              'Server name is required for connect action',\n            );\n          }\n          if (!params.server_command) {\n            return this.createErrorResult(\n              'Server command is required for connect action',\n            );\n          }\n\n          const server = await this.connectToServer(\n            params.server_name,\n            params.server_command,\n            [], // args - could be extended to accept from params\n            {}, // env - could be extended to accept from params\n          );\n\n          return this.createSuccessResult(\n            `Connected to MCP server \"${params.server_name}\"\\nAvailable tools: ${server.tools.join(', ')}`,\n            {\n              serverName: server.name,\n              connected: server.connected,\n              toolCount: server.tools.length,\n              tools: server.tools,\n            },\n          );\n        }\n\n        case 'disconnect': {\n          if (!params.server_name) {\n            return this.createErrorResult(\n              'Server name is required for disconnect action',\n            );\n          }\n\n          const server = this.servers.get(params.server_name);\n          if (!server) {\n            return this.createErrorResult(\n              `Server not found: ${params.server_name}`,\n            );\n          }\n\n          await this.disconnectFromServer(params.server_name);\n\n          return this.createSuccessResult(\n            `Disconnected from MCP server \"${params.server_name}\"`,\n          );\n        }\n\n        case 'call': {\n          if (!params.server_name) {\n            return this.createErrorResult(\n              'Server name is required for call action',\n            );\n          }\n          if (!params.tool_name) {\n            return this.createErrorResult(\n              'Tool name is required for call action',\n            );\n          }\n\n          const result = await this.callServerTool(\n            params.server_name,\n            params.tool_name,\n            params.tool_params || {},\n          );\n\n          let output = `Called tool \"${params.tool_name}\" on server \"${params.server_name}\"\\n\\n`;\n          output += `Result:\\n${JSON.stringify(result, null, 2)}`;\n\n          return this.createSuccessResult(output, {\n            serverName: params.server_name,\n            toolName: params.tool_name,\n            result,\n          });\n        }\n\n        default:\n          return this.createErrorResult(`Unknown action: ${params.action}`);\n      }\n    } catch (error) {\n      return this.createErrorResult(getErrorMessage(error));\n    }\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA,EACE;AAAA,OAIK;AACP,SAAS,uBAAuB;AAChC,SAAS,cAAc;AACvB,SAAS,4BAA4B;AAsB9B,MAAM,sBAAsB,KAA0B;AAAA,EAC3D,OAAgB,OAAO;AAAA,EACf,UAAkC,oBAAI,IAAI;AAAA,EAElD,cAAc;AACZ;AAAA,MACE,cAAc;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EAEA,gBAAgC;AAC9B,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,aAAa,KAAK;AAAA,MAClB,aAAa;AAAA,QACX,MAAM;AAAA,QACN,YAAY;AAAA,UACV,QAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM,CAAC,QAAQ,WAAW,cAAc,MAAM;AAAA,YAC9C,aAAa;AAAA,UACf;AAAA,UACA,aAAa;AAAA,YACX,MAAM;AAAA,YACN,aACE;AAAA,UACJ;AAAA,UACA,gBAAgB;AAAA,YACd,MAAM;AAAA,YACN,aACE;AAAA,UACJ;AAAA,UACA,WAAW;AAAA,YACT,MAAM;AAAA,YACN,aAAa;AAAA,UACf;AAAA,UACA,aAAa;AAAA,YACX,MAAM;AAAA,YACN,aAAa;AAAA,YACb,sBAAsB;AAAA,UACxB;AAAA,QACF;AAAA,QACA,UAAU,CAAC,QAAQ;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAc,gBACZ,MACA,SACA,OAAiB,CAAC,GAClB,MAA8B,CAAC,GACX;AACpB,QAAI;AAEF,YAAM,eAAe,QAAQ,MAAM,GAAG;AACtC,YAAM,aAAa,aAAa,CAAC;AACjC,YAAM,cAAc,CAAC,GAAG,aAAa,MAAM,CAAC,GAAG,GAAG,IAAI;AAItD,YAAM,WAAmC,CAAC;AAC1C,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,EAAE,GAAG,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG;AACrE,YAAI,UAAU,QAAW;AACvB,mBAAS,GAAG,IAAI;AAAA,QAClB;AAAA,MACF;AAEA,YAAM,YAAY,IAAI,qBAAqB;AAAA,QACzC,SAAS;AAAA,QACT,MAAM;AAAA,QACN,KAAK;AAAA,MACP,CAAC;AAED,YAAM,SAAS,IAAI;AAAA,QACjB;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,cAAc;AAAA,YACZ,OAAO,CAAC;AAAA,YACR,WAAW,CAAC;AAAA,YACZ,SAAS,CAAC;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAGA,YAAM,OAAO,QAAQ,SAAS;AAG9B,YAAM,gBAAgB,MAAM,OAAO,UAAU;AAC7C,YAAM,YAAY,cAAc,MAAM,IAAI,UAAQ,KAAK,IAAI;AAE3D,YAAM,SAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAEA,WAAK,QAAQ,IAAI,MAAM,MAAM;AAC7B,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,SAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX,OAAO,CAAC;AAAA,QACR,WAAW,gBAAgB,KAAK;AAAA,MAClC;AAEA,WAAK,QAAQ,IAAI,MAAM,MAAM;AAC7B,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,MAAc,qBAAqB,MAA6B;AAC9D,UAAM,SAAS,KAAK,QAAQ,IAAI,IAAI;AACpC,QAAI,QAAQ;AACV,UAAI;AAEF,YAAI,OAAO,WAAW;AACpB,gBAAM,OAAO,UAAU,MAAM;AAAA,QAC/B;AAAA,MACF,SAAS,OAAO;AAEd,eAAO,YAAY,gBAAgB,KAAK;AAAA,MAC1C;AAEA,aAAO,YAAY;AACnB,aAAO,QAAQ,CAAC;AAChB,aAAO,SAAS;AAChB,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,MAAc,eACZ,YACA,UACA,QACc;AACd,UAAM,SAAS,KAAK,QAAQ,IAAI,UAAU;AAC1C,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,qBAAqB,UAAU,EAAE;AAAA,IACnD;AAEA,QAAI,CAAC,OAAO,aAAa,CAAC,OAAO,QAAQ;AACvC,YAAM,IAAI,MAAM,yBAAyB,UAAU,EAAE;AAAA,IACvD;AAEA,QAAI,CAAC,OAAO,MAAM,SAAS,QAAQ,GAAG;AACpC,YAAM,IAAI,MAAM,uBAAuB,QAAQ,EAAE;AAAA,IACnD;AAEA,QAAI;AAEF,YAAM,SAAS,MAAM,OAAO,OAAO,SAAS;AAAA,QAC1C,MAAM;AAAA,QACN,WAAW,UAAU,CAAC;AAAA,MACxB,CAAC;AAED,aAAO;AAAA,QACL,QAAQ,OAAO;AAAA,QACf;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MACpC;AAAA,IACF,SAAS,OAAO;AACd,YAAM,IAAI,MAAM,qBAAqB,gBAAgB,KAAK,CAAC,EAAE;AAAA,IAC/D;AAAA,EACF;AAAA,EAEQ,mBAA2B;AACjC,QAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,gBAAgB,KAAK,QAAQ,IAAI;AAAA;AAAA;AAE9C,eAAW,CAAC,MAAM,MAAM,KAAK,KAAK,SAAS;AACzC,YAAM,SAAS,OAAO,YAAY,wBAAiB;AACnD,gBAAU,aAAM,IAAI;AAAA;AACpB,gBAAU,cAAc,MAAM;AAAA;AAC9B,gBAAU,eAAe,OAAO,OAAO;AAAA;AACvC,gBAAU,aAAa,OAAO,MAAM,SAAS,IAAI,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM;AAAA;AAEjF,UAAI,OAAO,WAAW;AACpB,kBAAU,kBAAkB,OAAO,SAAS;AAAA;AAAA,MAC9C;AAEA,gBAAU;AAAA,IACZ;AAEA,WAAO,OAAO,KAAK;AAAA,EACrB;AAAA,EAEA,MAAM,QACJ,QACA,SACqB;AACrB,QAAI;AACF,cAAQ,OAAO,QAAQ;AAAA,QACrB,KAAK,QAAQ;AACX,gBAAM,SAAS,KAAK,iBAAiB;AACrC,iBAAO,KAAK,oBAAoB,QAAQ;AAAA,YACtC,aAAa,KAAK,QAAQ;AAAA,YAC1B,kBAAkB,MAAM,KAAK,KAAK,QAAQ,OAAO,CAAC,EAAE;AAAA,cAClD,OAAK,EAAE;AAAA,YACT,EAAE;AAAA,UACJ,CAAC;AAAA,QACH;AAAA,QAEA,KAAK,WAAW;AACd,cAAI,CAAC,OAAO,aAAa;AACvB,mBAAO,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,OAAO,gBAAgB;AAC1B,mBAAO,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,SAAS,MAAM,KAAK;AAAA,YACxB,OAAO;AAAA,YACP,OAAO;AAAA,YACP,CAAC;AAAA;AAAA,YACD,CAAC;AAAA;AAAA,UACH;AAEA,iBAAO,KAAK;AAAA,YACV,4BAA4B,OAAO,WAAW;AAAA,mBAAuB,OAAO,MAAM,KAAK,IAAI,CAAC;AAAA,YAC5F;AAAA,cACE,YAAY,OAAO;AAAA,cACnB,WAAW,OAAO;AAAA,cAClB,WAAW,OAAO,MAAM;AAAA,cACxB,OAAO,OAAO;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,QAEA,KAAK,cAAc;AACjB,cAAI,CAAC,OAAO,aAAa;AACvB,mBAAO,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,SAAS,KAAK,QAAQ,IAAI,OAAO,WAAW;AAClD,cAAI,CAAC,QAAQ;AACX,mBAAO,KAAK;AAAA,cACV,qBAAqB,OAAO,WAAW;AAAA,YACzC;AAAA,UACF;AAEA,gBAAM,KAAK,qBAAqB,OAAO,WAAW;AAElD,iBAAO,KAAK;AAAA,YACV,iCAAiC,OAAO,WAAW;AAAA,UACrD;AAAA,QACF;AAAA,QAEA,KAAK,QAAQ;AACX,cAAI,CAAC,OAAO,aAAa;AACvB,mBAAO,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,OAAO,WAAW;AACrB,mBAAO,KAAK;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,SAAS,MAAM,KAAK;AAAA,YACxB,OAAO;AAAA,YACP,OAAO;AAAA,YACP,OAAO,eAAe,CAAC;AAAA,UACzB;AAEA,cAAI,SAAS,gBAAgB,OAAO,SAAS,gBAAgB,OAAO,WAAW;AAAA;AAAA;AAC/E,oBAAU;AAAA,EAAY,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;AAErD,iBAAO,KAAK,oBAAoB,QAAQ;AAAA,YACtC,YAAY,OAAO;AAAA,YACnB,UAAU,OAAO;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QAEA;AACE,iBAAO,KAAK,kBAAkB,mBAAmB,OAAO,MAAM,EAAE;AAAA,MACpE;AAAA,IACF,SAAS,OAAO;AACd,aAAO,KAAK,kBAAkB,gBAAgB,KAAK,CAAC;AAAA,IACtD;AAAA,EACF;AACF;", "names": []}