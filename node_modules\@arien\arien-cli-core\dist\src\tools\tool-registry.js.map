{"version": 3, "sources": ["../../../src/tools/tool-registry.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Config } from '../config/config.js';\nimport { logger } from '../core/logger.js';\nimport {\n  Tool,\n  ToolDefinition,\n  ToolParams,\n  ToolResult,\n  ToolExecutionContext,\n  ToolUsageTracker,\n} from './tools.js';\nimport { ToolExecutionError } from '../utils/errors.js';\nimport { MCPToolRegistry, MCPTool } from './mcp-tool.js';\n\nexport interface RegisteredTool {\n  tool: Tool;\n  enabled: boolean;\n  category?: string;\n  metadata?: Record<string, any>;\n}\n\nexport interface ToolExecutionOptions {\n  timeout?: number;\n  retries?: number;\n  trackUsage?: boolean;\n}\n\nexport class ToolRegistry {\n  private tools: Map<string, RegisteredTool> = new Map();\n  private config: Config;\n  private usageTracker: ToolUsageTracker;\n  private initialized: boolean = false;\n  private mcpToolRegistry: MCPToolRegistry;\n\n  constructor(config: Config) {\n    this.config = config;\n    this.usageTracker = new ToolUsageTracker();\n    this.mcpToolRegistry = new MCPToolRegistry();\n  }\n\n  /**\n   * Initialize the registry with built-in tools\n   * This is called automatically when tools are first accessed\n   */\n  private async initializeBuiltInTools(): Promise<void> {\n    if (this.initialized) {\n      return;\n    }\n\n    try {\n      // Import tool classes dynamically to avoid circular dependencies\n      const { ReadFileTool } = await import('./read-file.js');\n      const { WriteFileTool } = await import('./write-file.js');\n      const { ShellTool } = await import('./shell.js');\n      const { LsTool } = await import('./ls.js');\n      const { GrepTool } = await import('./grep.js');\n      const { GlobTool } = await import('./glob.js');\n      const { EditTool } = await import('./edit.js');\n      const { WebFetchTool } = await import('./web-fetch.js');\n      const { WebSearchTool } = await import('./web-search.js');\n      const { MemoryTool } = await import('./memory.js');\n      const { ReadManyFilesTool } = await import('./read-many-files.js');\n      const { MCPClientTool } = await import('./mcp-client.js');\n\n      // Register built-in tools\n      const builtInTools = [\n        { tool: new ReadFileTool(this.config), category: 'file-system' },\n        { tool: new WriteFileTool(this.config), category: 'file-system' },\n        { tool: new ShellTool(this.config), category: 'system' },\n        { tool: new LsTool(this.config), category: 'file-system' },\n        { tool: new GrepTool(this.config), category: 'file-system' },\n        { tool: new GlobTool(this.config), category: 'file-system' },\n        { tool: new EditTool(this.config), category: 'file-system' },\n        { tool: new WebFetchTool(this.config), category: 'web' },\n        { tool: new WebSearchTool(this.config), category: 'web' },\n        { tool: new MemoryTool(this.config), category: 'utility' },\n        { tool: new ReadManyFilesTool(this.config), category: 'file-system' },\n        { tool: new MCPClientTool(), category: 'mcp' },\n      ];\n\n      for (const { tool, category } of builtInTools) {\n        this.register(tool, { category });\n      }\n\n      this.initialized = true;\n      logger.debug(`Initialized ToolRegistry with ${builtInTools.length} built-in tools`);\n    } catch (error) {\n      logger.error('Failed to initialize built-in tools:', error);\n      this.initialized = true; // Prevent retry loops\n    }\n  }\n\n  register(\n    tool: Tool,\n    options: {\n      enabled?: boolean;\n      category?: string;\n      metadata?: Record<string, any>;\n    } = {},\n  ): void {\n    const { enabled = true, category, metadata } = options;\n\n    this.tools.set(tool.name, {\n      tool,\n      enabled,\n      category,\n      metadata,\n    });\n\n    logger.debug(`Registered tool: ${tool.name}`, {\n      enabled,\n      category,\n      description: tool.description,\n    });\n  }\n\n  unregister(toolName: string): boolean {\n    const removed = this.tools.delete(toolName);\n    if (removed) {\n      logger.debug(`Unregistered tool: ${toolName}`);\n    }\n    return removed;\n  }\n\n  async get(toolName: string): Promise<Tool | undefined> {\n    await this.initializeBuiltInTools();\n\n    // Check regular tools first\n    const registered = this.tools.get(toolName);\n    if (registered?.enabled) {\n      return registered.tool;\n    }\n\n    // Check MCP tools\n    const mcpTool = this.mcpToolRegistry.getTool(toolName);\n    return mcpTool;\n  }\n\n  /**\n   * Alias for get() method for backward compatibility\n   */\n  async getTool(toolName: string): Promise<Tool | undefined> {\n    return this.get(toolName);\n  }\n\n  async has(toolName: string): Promise<boolean> {\n    await this.initializeBuiltInTools();\n\n    // Check regular tools first\n    const registered = this.tools.get(toolName);\n    if (registered?.enabled) {\n      return true;\n    }\n\n    // Check MCP tools\n    return this.mcpToolRegistry.getTool(toolName) !== undefined;\n  }\n\n  async list(): Promise<string[]> {\n    await this.initializeBuiltInTools();\n    const regularTools = Array.from(this.tools.entries())\n      .filter(([, registered]) => registered.enabled)\n      .map(([name]) => name);\n\n    // Include MCP tools\n    const mcpTools = this.mcpToolRegistry.getAllTools().map(tool => tool.name);\n\n    return [...regularTools, ...mcpTools];\n  }\n\n  async listAll(): Promise<string[]> {\n    await this.initializeBuiltInTools();\n    return Array.from(this.tools.keys());\n  }\n\n  async getDefinitions(): Promise<ToolDefinition[]> {\n    await this.initializeBuiltInTools();\n    return Array.from(this.tools.entries())\n      .filter(([, registered]) => registered.enabled)\n      .map(([, registered]) => registered.tool.getDefinition());\n  }\n\n  async getDefinition(toolName: string): Promise<ToolDefinition | undefined> {\n    const tool = await this.get(toolName);\n    return tool?.getDefinition();\n  }\n\n  async enable(toolName: string): Promise<boolean> {\n    await this.initializeBuiltInTools();\n    const registered = this.tools.get(toolName);\n    if (registered) {\n      registered.enabled = true;\n      logger.debug(`Enabled tool: ${toolName}`);\n      return true;\n    }\n    return false;\n  }\n\n  async disable(toolName: string): Promise<boolean> {\n    await this.initializeBuiltInTools();\n    const registered = this.tools.get(toolName);\n    if (registered) {\n      registered.enabled = false;\n      logger.debug(`Disabled tool: ${toolName}`);\n      return true;\n    }\n    return false;\n  }\n\n  async execute(\n    toolName: string,\n    params: ToolParams,\n    context: ToolExecutionContext,\n    options: ToolExecutionOptions = {},\n  ): Promise<ToolResult> {\n    const { timeout = 30000, retries = 0, trackUsage = true } = options;\n\n    const tool = await this.get(toolName);\n    if (!tool) {\n      throw new ToolExecutionError(\n        `Tool not found or disabled: ${toolName}`,\n        toolName,\n      );\n    }\n\n    const startTime = Date.now();\n    let lastError: Error | undefined;\n\n    for (let attempt = 0; attempt <= retries; attempt++) {\n      try {\n        logger.debug(`Executing tool: ${toolName}`, {\n          attempt: attempt + 1,\n          maxAttempts: retries + 1,\n          params,\n        });\n\n        const result = await this.executeWithTimeout(\n          tool,\n          params,\n          context,\n          timeout,\n        );\n\n        const executionTime = Date.now() - startTime;\n\n        if (trackUsage) {\n          this.usageTracker.recordExecution(\n            toolName,\n            result.success,\n            executionTime,\n          );\n        }\n\n        logger.debug(`Tool execution completed: ${toolName}`, {\n          success: result.success,\n          executionTime,\n          attempt: attempt + 1,\n        });\n\n        return result;\n      } catch (error) {\n        lastError = error instanceof Error ? error : new Error(String(error));\n\n        logger.warn(`Tool execution failed: ${toolName}`, {\n          attempt: attempt + 1,\n          maxAttempts: retries + 1,\n          error: lastError.message,\n        });\n\n        if (attempt === retries) {\n          break;\n        }\n\n        // Wait before retry\n        await new Promise(resolve =>\n          setTimeout(resolve, Math.pow(2, attempt) * 1000),\n        );\n      }\n    }\n\n    const executionTime = Date.now() - startTime;\n\n    if (trackUsage) {\n      this.usageTracker.recordExecution(toolName, false, executionTime);\n    }\n\n    throw new ToolExecutionError(\n      `Tool execution failed after ${retries + 1} attempts: ${lastError?.message || 'Unknown error'}`,\n      toolName,\n      { originalError: lastError?.message, attempts: retries + 1 },\n    );\n  }\n\n  private async executeWithTimeout(\n    tool: Tool,\n    params: ToolParams,\n    context: ToolExecutionContext,\n    timeout: number,\n  ): Promise<ToolResult> {\n    return new Promise((resolve, reject) => {\n      const timeoutId = setTimeout(() => {\n        reject(new Error(`Tool execution timed out after ${timeout}ms`));\n      }, timeout);\n\n      tool\n        .execute(params, context)\n        .then(result => {\n          clearTimeout(timeoutId);\n          resolve(result);\n        })\n        .catch(error => {\n          clearTimeout(timeoutId);\n          reject(error);\n        });\n    });\n  }\n\n  getUsageStats(toolName?: string) {\n    if (toolName) {\n      return this.usageTracker.getStats(toolName);\n    }\n    return this.usageTracker.getAllStats();\n  }\n\n  getTopTools(limit?: number) {\n    return this.usageTracker.getTopTools(limit);\n  }\n\n  resetUsageStats(): void {\n    this.usageTracker.reset();\n  }\n\n  async getToolsByCategory(category: string): Promise<string[]> {\n    await this.initializeBuiltInTools();\n    return Array.from(this.tools.entries())\n      .filter(\n        ([, registered]) =>\n          registered.enabled && registered.category === category,\n      )\n      .map(([name]) => name);\n  }\n\n  async getCategories(): Promise<string[]> {\n    await this.initializeBuiltInTools();\n    const categories = new Set<string>();\n    for (const [, registered] of this.tools) {\n      if (registered.enabled && registered.category) {\n        categories.add(registered.category);\n      }\n    }\n    return Array.from(categories);\n  }\n\n  async validateTool(\n    toolName: string,\n    params: ToolParams,\n  ): Promise<{ valid: boolean; errors: string[] }> {\n    const tool = await this.get(toolName);\n    if (!tool) {\n      return { valid: false, errors: [`Tool not found: ${toolName}`] };\n    }\n\n    const definition = tool.getDefinition();\n    const errors: string[] = [];\n\n    // Check required parameters\n    const required = definition.parameters.required || [];\n    for (const requiredParam of required) {\n      if (!(requiredParam in params)) {\n        errors.push(`Missing required parameter: ${requiredParam}`);\n      }\n    }\n\n    // Check parameter types (basic validation)\n    for (const [paramName, paramValue] of Object.entries(params)) {\n      const paramDef = definition.parameters.properties[paramName];\n      if (!paramDef) {\n        errors.push(`Unknown parameter: ${paramName}`);\n        continue;\n      }\n\n      // Basic type checking\n      if (paramDef.type === 'string' && typeof paramValue !== 'string') {\n        errors.push(`Parameter ${paramName} must be a string`);\n      } else if (paramDef.type === 'number' && typeof paramValue !== 'number') {\n        errors.push(`Parameter ${paramName} must be a number`);\n      } else if (\n        paramDef.type === 'boolean' &&\n        typeof paramValue !== 'boolean'\n      ) {\n        errors.push(`Parameter ${paramName} must be a boolean`);\n      } else if (paramDef.type === 'array' && !Array.isArray(paramValue)) {\n        errors.push(`Parameter ${paramName} must be an array`);\n      }\n\n      // Check enum values\n      if (paramDef.enum && !paramDef.enum.includes(paramValue)) {\n        errors.push(\n          `Parameter ${paramName} must be one of: ${paramDef.enum.join(', ')}`,\n        );\n      }\n    }\n\n    return { valid: errors.length === 0, errors };\n  }\n\n  // Bulk operations\n  enableAll(): void {\n    for (const [toolName, registered] of this.tools) {\n      registered.enabled = true;\n    }\n    logger.debug('Enabled all tools');\n  }\n\n  disableAll(): void {\n    for (const [toolName, registered] of this.tools) {\n      registered.enabled = false;\n    }\n    logger.debug('Disabled all tools');\n  }\n\n  enableCategory(category: string): void {\n    for (const [toolName, registered] of this.tools) {\n      if (registered.category === category) {\n        registered.enabled = true;\n      }\n    }\n    logger.debug(`Enabled tools in category: ${category}`);\n  }\n\n  disableCategory(category: string): void {\n    for (const [toolName, registered] of this.tools) {\n      if (registered.category === category) {\n        registered.enabled = false;\n      }\n    }\n    logger.debug(`Disabled tools in category: ${category}`);\n  }\n\n  // MCP Tool Management\n  /**\n   * Register an MCP tool\n   */\n  registerMCPTool(tool: MCPTool): void {\n    this.mcpToolRegistry.registerTool(tool);\n    logger.debug(`Registered MCP tool: ${tool.name} from server: ${tool.getServerName()}`);\n  }\n\n  /**\n   * Unregister an MCP tool\n   */\n  unregisterMCPTool(toolName: string): boolean {\n    const result = this.mcpToolRegistry.unregisterTool(toolName);\n    if (result) {\n      logger.debug(`Unregistered MCP tool: ${toolName}`);\n    }\n    return result;\n  }\n\n  /**\n   * Unregister all tools from an MCP server\n   */\n  unregisterMCPServerTools(serverName: string): number {\n    const count = this.mcpToolRegistry.unregisterServerTools(serverName);\n    logger.debug(`Unregistered ${count} MCP tools from server: ${serverName}`);\n    return count;\n  }\n\n  /**\n   * Get MCP tools from a specific server\n   */\n  getMCPServerTools(serverName: string): MCPTool[] {\n    return this.mcpToolRegistry.getServerTools(serverName);\n  }\n\n  /**\n   * Get all connected MCP servers\n   */\n  getConnectedMCPServers(): string[] {\n    return this.mcpToolRegistry.getConnectedServers();\n  }\n\n  /**\n   * Get the MCP tool registry instance\n   */\n  getMCPToolRegistry(): MCPToolRegistry {\n    return this.mcpToolRegistry;\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,cAAc;AACvB;AAAA,EAME;AAAA,OACK;AACP,SAAS,0BAA0B;AACnC,SAAS,uBAAgC;AAelC,MAAM,aAAa;AAAA,EAChB,QAAqC,oBAAI,IAAI;AAAA,EAC7C;AAAA,EACA;AAAA,EACA,cAAuB;AAAA,EACvB;AAAA,EAER,YAAY,QAAgB;AAC1B,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,iBAAiB;AACzC,SAAK,kBAAkB,IAAI,gBAAgB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,yBAAwC;AACpD,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AAEA,QAAI;AAEF,YAAM,EAAE,aAAa,IAAI,MAAM,OAAO,gBAAgB;AACtD,YAAM,EAAE,cAAc,IAAI,MAAM,OAAO,iBAAiB;AACxD,YAAM,EAAE,UAAU,IAAI,MAAM,OAAO,YAAY;AAC/C,YAAM,EAAE,OAAO,IAAI,MAAM,OAAO,SAAS;AACzC,YAAM,EAAE,SAAS,IAAI,MAAM,OAAO,WAAW;AAC7C,YAAM,EAAE,SAAS,IAAI,MAAM,OAAO,WAAW;AAC7C,YAAM,EAAE,SAAS,IAAI,MAAM,OAAO,WAAW;AAC7C,YAAM,EAAE,aAAa,IAAI,MAAM,OAAO,gBAAgB;AACtD,YAAM,EAAE,cAAc,IAAI,MAAM,OAAO,iBAAiB;AACxD,YAAM,EAAE,WAAW,IAAI,MAAM,OAAO,aAAa;AACjD,YAAM,EAAE,kBAAkB,IAAI,MAAM,OAAO,sBAAsB;AACjE,YAAM,EAAE,cAAc,IAAI,MAAM,OAAO,iBAAiB;AAGxD,YAAM,eAAe;AAAA,QACnB,EAAE,MAAM,IAAI,aAAa,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QAC/D,EAAE,MAAM,IAAI,cAAc,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QAChE,EAAE,MAAM,IAAI,UAAU,KAAK,MAAM,GAAG,UAAU,SAAS;AAAA,QACvD,EAAE,MAAM,IAAI,OAAO,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QACzD,EAAE,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QAC3D,EAAE,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QAC3D,EAAE,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QAC3D,EAAE,MAAM,IAAI,aAAa,KAAK,MAAM,GAAG,UAAU,MAAM;AAAA,QACvD,EAAE,MAAM,IAAI,cAAc,KAAK,MAAM,GAAG,UAAU,MAAM;AAAA,QACxD,EAAE,MAAM,IAAI,WAAW,KAAK,MAAM,GAAG,UAAU,UAAU;AAAA,QACzD,EAAE,MAAM,IAAI,kBAAkB,KAAK,MAAM,GAAG,UAAU,cAAc;AAAA,QACpE,EAAE,MAAM,IAAI,cAAc,GAAG,UAAU,MAAM;AAAA,MAC/C;AAEA,iBAAW,EAAE,MAAM,SAAS,KAAK,cAAc;AAC7C,aAAK,SAAS,MAAM,EAAE,SAAS,CAAC;AAAA,MAClC;AAEA,WAAK,cAAc;AACnB,aAAO,MAAM,iCAAiC,aAAa,MAAM,iBAAiB;AAAA,IACpF,SAAS,OAAO;AACd,aAAO,MAAM,wCAAwC,KAAK;AAC1D,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,SACE,MACA,UAII,CAAC,GACC;AACN,UAAM,EAAE,UAAU,MAAM,UAAU,SAAS,IAAI;AAE/C,SAAK,MAAM,IAAI,KAAK,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,WAAO,MAAM,oBAAoB,KAAK,IAAI,IAAI;AAAA,MAC5C;AAAA,MACA;AAAA,MACA,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,WAAW,UAA2B;AACpC,UAAM,UAAU,KAAK,MAAM,OAAO,QAAQ;AAC1C,QAAI,SAAS;AACX,aAAO,MAAM,sBAAsB,QAAQ,EAAE;AAAA,IAC/C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,IAAI,UAA6C;AACrD,UAAM,KAAK,uBAAuB;AAGlC,UAAM,aAAa,KAAK,MAAM,IAAI,QAAQ;AAC1C,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW;AAAA,IACpB;AAGA,UAAM,UAAU,KAAK,gBAAgB,QAAQ,QAAQ;AACrD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,UAA6C;AACzD,WAAO,KAAK,IAAI,QAAQ;AAAA,EAC1B;AAAA,EAEA,MAAM,IAAI,UAAoC;AAC5C,UAAM,KAAK,uBAAuB;AAGlC,UAAM,aAAa,KAAK,MAAM,IAAI,QAAQ;AAC1C,QAAI,YAAY,SAAS;AACvB,aAAO;AAAA,IACT;AAGA,WAAO,KAAK,gBAAgB,QAAQ,QAAQ,MAAM;AAAA,EACpD;AAAA,EAEA,MAAM,OAA0B;AAC9B,UAAM,KAAK,uBAAuB;AAClC,UAAM,eAAe,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,EACjD,OAAO,CAAC,CAAC,EAAE,UAAU,MAAM,WAAW,OAAO,EAC7C,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI;AAGvB,UAAM,WAAW,KAAK,gBAAgB,YAAY,EAAE,IAAI,UAAQ,KAAK,IAAI;AAEzE,WAAO,CAAC,GAAG,cAAc,GAAG,QAAQ;AAAA,EACtC;AAAA,EAEA,MAAM,UAA6B;AACjC,UAAM,KAAK,uBAAuB;AAClC,WAAO,MAAM,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,EACrC;AAAA,EAEA,MAAM,iBAA4C;AAChD,UAAM,KAAK,uBAAuB;AAClC,WAAO,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,EACnC,OAAO,CAAC,CAAC,EAAE,UAAU,MAAM,WAAW,OAAO,EAC7C,IAAI,CAAC,CAAC,EAAE,UAAU,MAAM,WAAW,KAAK,cAAc,CAAC;AAAA,EAC5D;AAAA,EAEA,MAAM,cAAc,UAAuD;AACzE,UAAM,OAAO,MAAM,KAAK,IAAI,QAAQ;AACpC,WAAO,MAAM,cAAc;AAAA,EAC7B;AAAA,EAEA,MAAM,OAAO,UAAoC;AAC/C,UAAM,KAAK,uBAAuB;AAClC,UAAM,aAAa,KAAK,MAAM,IAAI,QAAQ;AAC1C,QAAI,YAAY;AACd,iBAAW,UAAU;AACrB,aAAO,MAAM,iBAAiB,QAAQ,EAAE;AACxC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,QAAQ,UAAoC;AAChD,UAAM,KAAK,uBAAuB;AAClC,UAAM,aAAa,KAAK,MAAM,IAAI,QAAQ;AAC1C,QAAI,YAAY;AACd,iBAAW,UAAU;AACrB,aAAO,MAAM,kBAAkB,QAAQ,EAAE;AACzC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,QACJ,UACA,QACA,SACA,UAAgC,CAAC,GACZ;AACrB,UAAM,EAAE,UAAU,KAAO,UAAU,GAAG,aAAa,KAAK,IAAI;AAE5D,UAAM,OAAO,MAAM,KAAK,IAAI,QAAQ;AACpC,QAAI,CAAC,MAAM;AACT,YAAM,IAAI;AAAA,QACR,+BAA+B,QAAQ;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,YAAY,KAAK,IAAI;AAC3B,QAAI;AAEJ,aAAS,UAAU,GAAG,WAAW,SAAS,WAAW;AACnD,UAAI;AACF,eAAO,MAAM,mBAAmB,QAAQ,IAAI;AAAA,UAC1C,SAAS,UAAU;AAAA,UACnB,aAAa,UAAU;AAAA,UACvB;AAAA,QACF,CAAC;AAED,cAAM,SAAS,MAAM,KAAK;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,cAAMA,iBAAgB,KAAK,IAAI,IAAI;AAEnC,YAAI,YAAY;AACd,eAAK,aAAa;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,YACPA;AAAA,UACF;AAAA,QACF;AAEA,eAAO,MAAM,6BAA6B,QAAQ,IAAI;AAAA,UACpD,SAAS,OAAO;AAAA,UAChB,eAAAA;AAAA,UACA,SAAS,UAAU;AAAA,QACrB,CAAC;AAED,eAAO;AAAA,MACT,SAAS,OAAO;AACd,oBAAY,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK,CAAC;AAEpE,eAAO,KAAK,0BAA0B,QAAQ,IAAI;AAAA,UAChD,SAAS,UAAU;AAAA,UACnB,aAAa,UAAU;AAAA,UACvB,OAAO,UAAU;AAAA,QACnB,CAAC;AAED,YAAI,YAAY,SAAS;AACvB;AAAA,QACF;AAGA,cAAM,IAAI;AAAA,UAAQ,aAChB,WAAW,SAAS,KAAK,IAAI,GAAG,OAAO,IAAI,GAAI;AAAA,QACjD;AAAA,MACF;AAAA,IACF;AAEA,UAAM,gBAAgB,KAAK,IAAI,IAAI;AAEnC,QAAI,YAAY;AACd,WAAK,aAAa,gBAAgB,UAAU,OAAO,aAAa;AAAA,IAClE;AAEA,UAAM,IAAI;AAAA,MACR,+BAA+B,UAAU,CAAC,cAAc,WAAW,WAAW,eAAe;AAAA,MAC7F;AAAA,MACA,EAAE,eAAe,WAAW,SAAS,UAAU,UAAU,EAAE;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,MAAc,mBACZ,MACA,QACA,SACA,SACqB;AACrB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,YAAY,WAAW,MAAM;AACjC,eAAO,IAAI,MAAM,kCAAkC,OAAO,IAAI,CAAC;AAAA,MACjE,GAAG,OAAO;AAEV,WACG,QAAQ,QAAQ,OAAO,EACvB,KAAK,YAAU;AACd,qBAAa,SAAS;AACtB,gBAAQ,MAAM;AAAA,MAChB,CAAC,EACA,MAAM,WAAS;AACd,qBAAa,SAAS;AACtB,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEA,cAAc,UAAmB;AAC/B,QAAI,UAAU;AACZ,aAAO,KAAK,aAAa,SAAS,QAAQ;AAAA,IAC5C;AACA,WAAO,KAAK,aAAa,YAAY;AAAA,EACvC;AAAA,EAEA,YAAY,OAAgB;AAC1B,WAAO,KAAK,aAAa,YAAY,KAAK;AAAA,EAC5C;AAAA,EAEA,kBAAwB;AACtB,SAAK,aAAa,MAAM;AAAA,EAC1B;AAAA,EAEA,MAAM,mBAAmB,UAAqC;AAC5D,UAAM,KAAK,uBAAuB;AAClC,WAAO,MAAM,KAAK,KAAK,MAAM,QAAQ,CAAC,EACnC;AAAA,MACC,CAAC,CAAC,EAAE,UAAU,MACZ,WAAW,WAAW,WAAW,aAAa;AAAA,IAClD,EACC,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI;AAAA,EACzB;AAAA,EAEA,MAAM,gBAAmC;AACvC,UAAM,KAAK,uBAAuB;AAClC,UAAM,aAAa,oBAAI,IAAY;AACnC,eAAW,CAAC,EAAE,UAAU,KAAK,KAAK,OAAO;AACvC,UAAI,WAAW,WAAW,WAAW,UAAU;AAC7C,mBAAW,IAAI,WAAW,QAAQ;AAAA,MACpC;AAAA,IACF;AACA,WAAO,MAAM,KAAK,UAAU;AAAA,EAC9B;AAAA,EAEA,MAAM,aACJ,UACA,QAC+C;AAC/C,UAAM,OAAO,MAAM,KAAK,IAAI,QAAQ;AACpC,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,OAAO,OAAO,QAAQ,CAAC,mBAAmB,QAAQ,EAAE,EAAE;AAAA,IACjE;AAEA,UAAM,aAAa,KAAK,cAAc;AACtC,UAAM,SAAmB,CAAC;AAG1B,UAAM,WAAW,WAAW,WAAW,YAAY,CAAC;AACpD,eAAW,iBAAiB,UAAU;AACpC,UAAI,EAAE,iBAAiB,SAAS;AAC9B,eAAO,KAAK,+BAA+B,aAAa,EAAE;AAAA,MAC5D;AAAA,IACF;AAGA,eAAW,CAAC,WAAW,UAAU,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC5D,YAAM,WAAW,WAAW,WAAW,WAAW,SAAS;AAC3D,UAAI,CAAC,UAAU;AACb,eAAO,KAAK,sBAAsB,SAAS,EAAE;AAC7C;AAAA,MACF;AAGA,UAAI,SAAS,SAAS,YAAY,OAAO,eAAe,UAAU;AAChE,eAAO,KAAK,aAAa,SAAS,mBAAmB;AAAA,MACvD,WAAW,SAAS,SAAS,YAAY,OAAO,eAAe,UAAU;AACvE,eAAO,KAAK,aAAa,SAAS,mBAAmB;AAAA,MACvD,WACE,SAAS,SAAS,aAClB,OAAO,eAAe,WACtB;AACA,eAAO,KAAK,aAAa,SAAS,oBAAoB;AAAA,MACxD,WAAW,SAAS,SAAS,WAAW,CAAC,MAAM,QAAQ,UAAU,GAAG;AAClE,eAAO,KAAK,aAAa,SAAS,mBAAmB;AAAA,MACvD;AAGA,UAAI,SAAS,QAAQ,CAAC,SAAS,KAAK,SAAS,UAAU,GAAG;AACxD,eAAO;AAAA,UACL,aAAa,SAAS,oBAAoB,SAAS,KAAK,KAAK,IAAI,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AAEA,WAAO,EAAE,OAAO,OAAO,WAAW,GAAG,OAAO;AAAA,EAC9C;AAAA;AAAA,EAGA,YAAkB;AAChB,eAAW,CAAC,UAAU,UAAU,KAAK,KAAK,OAAO;AAC/C,iBAAW,UAAU;AAAA,IACvB;AACA,WAAO,MAAM,mBAAmB;AAAA,EAClC;AAAA,EAEA,aAAmB;AACjB,eAAW,CAAC,UAAU,UAAU,KAAK,KAAK,OAAO;AAC/C,iBAAW,UAAU;AAAA,IACvB;AACA,WAAO,MAAM,oBAAoB;AAAA,EACnC;AAAA,EAEA,eAAe,UAAwB;AACrC,eAAW,CAAC,UAAU,UAAU,KAAK,KAAK,OAAO;AAC/C,UAAI,WAAW,aAAa,UAAU;AACpC,mBAAW,UAAU;AAAA,MACvB;AAAA,IACF;AACA,WAAO,MAAM,8BAA8B,QAAQ,EAAE;AAAA,EACvD;AAAA,EAEA,gBAAgB,UAAwB;AACtC,eAAW,CAAC,UAAU,UAAU,KAAK,KAAK,OAAO;AAC/C,UAAI,WAAW,aAAa,UAAU;AACpC,mBAAW,UAAU;AAAA,MACvB;AAAA,IACF;AACA,WAAO,MAAM,+BAA+B,QAAQ,EAAE;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,MAAqB;AACnC,SAAK,gBAAgB,aAAa,IAAI;AACtC,WAAO,MAAM,wBAAwB,KAAK,IAAI,iBAAiB,KAAK,cAAc,CAAC,EAAE;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,UAA2B;AAC3C,UAAM,SAAS,KAAK,gBAAgB,eAAe,QAAQ;AAC3D,QAAI,QAAQ;AACV,aAAO,MAAM,0BAA0B,QAAQ,EAAE;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,YAA4B;AACnD,UAAM,QAAQ,KAAK,gBAAgB,sBAAsB,UAAU;AACnE,WAAO,MAAM,gBAAgB,KAAK,2BAA2B,UAAU,EAAE;AACzE,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,YAA+B;AAC/C,WAAO,KAAK,gBAAgB,eAAe,UAAU;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAmC;AACjC,WAAO,KAAK,gBAAgB,oBAAoB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAsC;AACpC,WAAO,KAAK;AAAA,EACd;AACF;", "names": ["executionTime"]}