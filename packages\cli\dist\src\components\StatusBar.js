import { Fragment, jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from "react";
import { Box, Text } from "ink";
const StatusBar = ({
  config,
  isGenerating,
  error,
  screen,
  showServiceStatus = false
}) => {
  const [serviceStatus, setServiceStatus] = useState({
    fileService: false,
    gitService: false,
    mcpServers: 0
  });
  useEffect(() => {
    const checkServices = async () => {
      try {
        const fileService = config.getFileService();
        const fileServiceStatus = !!fileService;
        let gitServiceStatus = false;
        try {
          const gitService = config.getGitService();
          const gitStatus = await gitService.getStatus();
          gitServiceStatus = gitStatus.isRepository;
        } catch {
          gitServiceStatus = false;
        }
        let mcpServerCount = 0;
        try {
          const mcpManager = config.getMCPServerManager();
          const servers = mcpManager.getActiveServers();
          mcpServerCount = servers.length;
        } catch {
          mcpServerCount = 0;
        }
        setServiceStatus({
          fileService: fileServiceStatus,
          gitService: gitServiceStatus,
          mcpServers: mcpServerCount
        });
      } catch (error2) {
        console.debug("Service status check failed:", error2);
      }
    };
    if (showServiceStatus) {
      checkServices();
    }
  }, [config, showServiceStatus]);
  const getStatusColor = () => {
    if (error) return "red";
    if (isGenerating) return "yellow";
    return "green";
  };
  const getStatusText = () => {
    if (error) return "\u274C Error";
    if (isGenerating) return "\u23F3 Generating...";
    return "\u2705 Ready";
  };
  const formatModel = (model) => {
    return model.replace("gemini-2.0-flash-exp", "Gemini 2.0 Flash").replace("gemini-1.5-pro", "Gemini 1.5 Pro").replace("gemini-1.5-flash", "Gemini 1.5 Flash");
  };
  return /* @__PURE__ */ jsxs(
    Box,
    {
      borderStyle: "single",
      borderColor: "gray",
      paddingX: 1,
      justifyContent: "space-between",
      children: [
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: getStatusColor(), bold: true, children: getStatusText() }),
          /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: " | " }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", children: formatModel(config.getModel()) }),
          screen !== "chat" && /* @__PURE__ */ jsxs(Fragment, { children: [
            /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: " | " }),
            /* @__PURE__ */ jsx(Text, { color: "magenta", children: screen.charAt(0).toUpperCase() + screen.slice(1) })
          ] })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          showServiceStatus && /* @__PURE__ */ jsxs(Fragment, { children: [
            /* @__PURE__ */ jsx(Text, { color: serviceStatus.fileService ? "green" : "red", children: "\u{1F4C1}" }),
            /* @__PURE__ */ jsx(Text, { color: serviceStatus.gitService ? "green" : "yellow", children: serviceStatus.gitService ? "\u{1F517}" : "\u{1F4DD}" }),
            serviceStatus.mcpServers > 0 && /* @__PURE__ */ jsxs(Text, { color: "blue", children: [
              "\u{1F50C}",
              serviceStatus.mcpServers
            ] }),
            /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: " | " })
          ] }),
          error ? /* @__PURE__ */ jsx(Text, { color: "red", dimColor: true, children: error.length > 50 ? `${error.substring(0, 50)}...` : error }) : /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "Ctrl+C: Exit | Ctrl+L: Clear | Ctrl+H: Help" })
        ] })
      ]
    }
  );
};
export {
  StatusBar
};
//# sourceMappingURL=StatusBar.js.map
