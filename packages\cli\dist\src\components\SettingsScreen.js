import { jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Box, Text } from "ink";
const SettingsScreen = ({
  config,
  configErrors,
  isConfigValid
}) => {
  const getStatusIcon = (isValid) => {
    return isValid ? "\u2705" : "\u274C";
  };
  const getStatusColor = (isValid) => {
    return isValid ? "green" : "red";
  };
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", paddingX: 2, paddingY: 1, children: [
    /* @__PURE__ */ jsx(Text, { bold: true, color: "cyan", children: "\u2699\uFE0F Configuration Settings" }),
    /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 1, children: [
      /* @__PURE__ */ jsx(Box, { children: /* @__PURE__ */ jsxs(Text, { color: getStatusColor(isConfigValid), children: [
        getStatusIcon(isConfigValid),
        " Configuration Status: ",
        isConfigValid ? "Valid" : "Invalid"
      ] }) }),
      configErrors.length > 0 && /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 1, children: [
        /* @__PURE__ */ jsx(Text, { color: "red", bold: true, children: "\u274C Configuration Errors:" }),
        configErrors.map((error, index) => /* @__PURE__ */ jsx(Box, { marginLeft: 2, children: /* @__PURE__ */ jsxs(Text, { color: "red", children: [
          "\u2022 ",
          error
        ] }) }, index))
      ] }),
      /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 2, children: [
        /* @__PURE__ */ jsx(Text, { color: "yellow", bold: true, children: "\u{1F4CB} Current Configuration:" }),
        /* @__PURE__ */ jsxs(Box, { marginTop: 1, children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Model: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getModel() || "Not set" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Workspace Root: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getWorkspaceRoot() || "Not set" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Debug Mode: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getDebugMode() ? "Enabled" : "Disabled" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "All Files: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getAllFiles() ? "Enabled" : "Disabled" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Approval Mode: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getApprovalMode() })
        ] }),
        /* @__PURE__ */ jsxs(Box, { children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", children: "Auth Type: " }),
          /* @__PURE__ */ jsx(Text, { color: "white", children: config.getSelectedAuthType() || "Not set" })
        ] })
      ] }),
      (() => {
        const settingsErrors = config.getSettingsErrors();
        if (settingsErrors.length > 0) {
          return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 2, children: [
            /* @__PURE__ */ jsx(Text, { color: "red", bold: true, children: "\u{1F4C4} Settings File Errors:" }),
            settingsErrors.map((error, index) => /* @__PURE__ */ jsx(Box, { marginLeft: 2, children: /* @__PURE__ */ jsxs(Text, { color: "red", children: [
              "\u2022 ",
              error.path,
              ": ",
              error.message
            ] }) }, index))
          ] });
        }
        return null;
      })(),
      /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 2, children: [
        /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u{1F4A1} Help:" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "\u2022 Configuration files are located in ~/.arien/settings.json (user) and .arien/settings.json (workspace)" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "\u2022 Environment variables can be set in .env files" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "\u2022 Use command line arguments to override settings temporarily" })
      ] }),
      /* @__PURE__ */ jsxs(Box, { flexDirection: "column", marginTop: 2, borderStyle: "single", borderColor: "gray", padding: 1, children: [
        /* @__PURE__ */ jsx(Text, { color: "yellow", bold: true, children: "\u{1F3AE} Navigation:" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "[C] Start Chat (if config is valid)" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "[Q] Quit Application" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "[H] Show Help" }),
        /* @__PURE__ */ jsx(Text, { color: "gray", children: "[V] Service Status" })
      ] })
    ] })
  ] });
};
export {
  SettingsScreen
};
//# sourceMappingURL=SettingsScreen.js.map
