import { jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { Box, Text } from "ink";
const WelcomeScreen = ({
  config,
  onStartChat,
  onShowSettings,
  onShowHelp
}) => {
  const formatModel = (model) => {
    return model.replace("gemini-2.0-flash-exp", "Gemini 2.0 Flash Experimental").replace("gemini-1.5-pro", "Gemini 1.5 Pro").replace("gemini-1.5-flash", "Gemini 1.5 Flash");
  };
  return /* @__PURE__ */ jsxs(
    Box,
    {
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      flexGrow: 1,
      children: [
        /* @__PURE__ */ jsxs(Box, { flexDirection: "column", alignItems: "center", marginBottom: 2, children: [
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2554\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2557" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2551 \u2551" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2551 \u{1F916} ARIEN \u2551" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2551 \u2551" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2551 AI Assistant for Coding \u2551" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u2551 \u2551" }),
          /* @__PURE__ */ jsx(Text, { color: "cyan", bold: true, children: "\u255A\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u255D" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { flexDirection: "column", alignItems: "center", marginBottom: 2, children: [
          /* @__PURE__ */ jsx(Text, { color: "white", bold: true, children: "Current Configuration:" }),
          /* @__PURE__ */ jsxs(Text, { color: "green", children: [
            "Model: ",
            formatModel(config.getModel())
          ] }),
          /* @__PURE__ */ jsxs(Text, { color: "green", children: [
            "Workspace: ",
            config.getWorkspaceRoot()
          ] }),
          config.getDebug() && /* @__PURE__ */ jsx(Text, { color: "yellow", children: "Debug Mode: Enabled" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { flexDirection: "column", alignItems: "center", marginBottom: 2, children: [
          /* @__PURE__ */ jsx(Text, { color: "white", bold: true, children: "What would you like to do?" }),
          /* @__PURE__ */ jsx(Text, {}),
          /* @__PURE__ */ jsx(Text, { color: "cyan", children: "[C] Start Chat Session" }),
          /* @__PURE__ */ jsx(Text, { color: "yellow", children: "[S] Settings" }),
          /* @__PURE__ */ jsx(Text, { color: "green", children: "[V] Service Status" }),
          /* @__PURE__ */ jsx(Text, { color: "magenta", children: "[H] Help" }),
          /* @__PURE__ */ jsx(Text, { color: "red", children: "[Q] Quit" })
        ] }),
        /* @__PURE__ */ jsxs(Box, { flexDirection: "column", alignItems: "center", children: [
          /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "Press the corresponding key or Enter to start chatting" }),
          /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "Use Ctrl+C to exit at any time" })
        ] }),
        /* @__PURE__ */ jsx(Box, { position: "absolute", bottom: 0, right: 0, padding: 1, children: /* @__PURE__ */ jsx(Text, { color: "gray", dimColor: true, children: "v1.0.0" }) })
      ]
    }
  );
};
export {
  WelcomeScreen
};
//# sourceMappingURL=WelcomeScreen.js.map
