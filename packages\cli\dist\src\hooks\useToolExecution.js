/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback, useRef } from "react";
import {
  ApprovalMode
} from "@arien/arien-cli-core";
const useToolExecution = (config) => {
  const [pendingApproval, setPendingApproval] = useState(null);
  const [approvalMode, setApprovalMode] = useState(config.getApprovalMode());
  const approvalResolveRef = useRef(null);
  const approvedToolsRef = useRef(/* @__PURE__ */ new Set());
  const deniedToolsRef = useRef(/* @__PURE__ */ new Set());
  const approveTool = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      if (approvalMode === ApprovalMode.ONCE) {
        approvedToolsRef.current.add(pendingApproval.toolName);
      }
      approvalResolveRef.current({
        approved: true,
        rememberChoice: approvalMode === ApprovalMode.ONCE
      });
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, approvalMode]);
  const denyTool = useCallback((reason) => {
    if (pendingApproval && approvalResolveRef.current) {
      if (approvalMode === ApprovalMode.ONCE) {
        deniedToolsRef.current.add(pendingApproval.toolName);
      }
      approvalResolveRef.current({
        approved: false,
        reason: reason || "Tool execution denied by user",
        rememberChoice: approvalMode === ApprovalMode.ONCE
      });
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, approvalMode]);
  const approveAllTools = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      setApprovalMode(ApprovalMode.ALWAYS);
      config.setApprovalMode(ApprovalMode.ALWAYS);
      approvalResolveRef.current({
        approved: true,
        rememberChoice: true
      });
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, config]);
  const denyAllTools = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      setApprovalMode(ApprovalMode.NEVER);
      config.setApprovalMode(ApprovalMode.NEVER);
      approvalResolveRef.current({
        approved: false,
        reason: "All tool execution denied by user",
        rememberChoice: true
      });
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, config]);
  const getApprovalCallback = useCallback(() => {
    return async (request) => {
      const currentMode = config.getApprovalMode();
      if (currentMode === ApprovalMode.ALWAYS) {
        return {
          approved: true,
          rememberChoice: true
        };
      }
      if (currentMode === ApprovalMode.NEVER) {
        return {
          approved: false,
          reason: "Tool execution disabled by approval mode",
          rememberChoice: true
        };
      }
      if (currentMode === ApprovalMode.ONCE) {
        if (approvedToolsRef.current.has(request.toolName)) {
          return {
            approved: true,
            rememberChoice: true
          };
        }
        if (deniedToolsRef.current.has(request.toolName)) {
          return {
            approved: false,
            reason: "Tool execution previously denied",
            rememberChoice: true
          };
        }
      }
      return new Promise((resolve) => {
        setPendingApproval(request);
        approvalResolveRef.current = resolve;
      });
    };
  }, [config]);
  return {
    pendingApproval,
    approveTool,
    denyTool,
    approveAllTools,
    denyAllTools,
    getApprovalCallback
  };
};
export {
  useToolExecution
};
//# sourceMappingURL=useToolExecution.js.map
