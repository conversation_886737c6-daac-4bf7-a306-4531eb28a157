{"version": 3, "sources": ["../../../src/core/contentGenerator.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport {\n  CountTokensResponse,\n  GenerateContentResponse,\n  GenerateContentParameters,\n  CountTokensParameters,\n  EmbedContentResponse,\n  EmbedContentParameters,\n  GoogleGenAI,\n} from '@google/genai';\nimport { DEFAULT_GEMINI_MODEL } from '../config/models.js';\nimport { AuthType } from '../config/config.js';\n\n/**\n * Interface abstracting the core functionalities for generating content and counting tokens.\n */\nexport interface ContentGenerator {\n  generateContent(\n    request: GenerateContentParameters,\n  ): Promise<GenerateContentResponse>;\n\n  generateContentStream(\n    request: GenerateContentParameters,\n  ): Promise<AsyncGenerator<GenerateContentResponse>>;\n\n  countTokens(request: CountTokensParameters): Promise<CountTokensResponse>;\n\n  embedContent(request: EmbedContentParameters): Promise<EmbedContentResponse>;\n}\n\nexport type ContentGeneratorConfig = {\n  model: string;\n  apiKey?: string;\n  vertexai?: boolean;\n  authType?: AuthType | undefined;\n};\n\nexport async function createContentGeneratorConfig(\n  model?: string,\n  apiKey?: string,\n  authType?: AuthType,\n): Promise<ContentGeneratorConfig> {\n  const contentGeneratorConfig: ContentGeneratorConfig = {\n    model: model || DEFAULT_GEMINI_MODEL,\n    apiKey,\n    authType: authType || AuthType.USE_GEMINI,\n  };\n\n  return contentGeneratorConfig;\n}\n\nexport async function createContentGenerator(\n  config: ContentGeneratorConfig,\n  sessionId?: string,\n): Promise<ContentGenerator> {\n  const version = process.env.CLI_VERSION || process.version;\n  const httpOptions = {\n    headers: {\n      'User-Agent': `ArienCLI/${version} (${process.platform}; ${process.arch})`,\n    },\n  };\n\n  if (\n    config.authType === AuthType.USE_GEMINI ||\n    config.authType === AuthType.USE_VERTEX_AI\n  ) {\n    const googleGenAI = new GoogleGenAI({\n      apiKey: config.apiKey === '' ? undefined : config.apiKey,\n      vertexai: config.vertexai,\n      httpOptions,\n    });\n\n    return googleGenAI.models;\n  }\n\n  throw new Error(`Unsupported auth type: ${config.authType}`);\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAAA,EAOE;AAAA,OACK;AACP,SAAS,4BAA4B;AACrC,SAAS,gBAAgB;AA0BzB,eAAsB,6BACpB,OACA,QACA,UACiC;AACjC,QAAM,yBAAiD;AAAA,IACrD,OAAO,SAAS;AAAA,IAChB;AAAA,IACA,UAAU,YAAY,SAAS;AAAA,EACjC;AAEA,SAAO;AACT;AAEA,eAAsB,uBACpB,QACA,WAC2B;AAC3B,QAAM,UAAU,QAAQ,IAAI,eAAe,QAAQ;AACnD,QAAM,cAAc;AAAA,IAClB,SAAS;AAAA,MACP,cAAc,YAAY,OAAO,KAAK,QAAQ,QAAQ,KAAK,QAAQ,IAAI;AAAA,IACzE;AAAA,EACF;AAEA,MACE,OAAO,aAAa,SAAS,cAC7B,OAAO,aAAa,SAAS,eAC7B;AACA,UAAM,cAAc,IAAI,YAAY;AAAA,MAClC,QAAQ,OAAO,WAAW,KAAK,SAAY,OAAO;AAAA,MAClD,UAAU,OAAO;AAAA,MACjB;AAAA,IACF,CAAC;AAED,WAAO,YAAY;AAAA,EACrB;AAEA,QAAM,IAAI,MAAM,0BAA0B,OAAO,QAAQ,EAAE;AAC7D;", "names": []}