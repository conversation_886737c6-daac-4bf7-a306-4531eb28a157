/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useCallback } from 'react';
import { Box } from 'ink';
import { Config } from '@arien/arien-cli-core';
import { ChatMessage } from '../hooks/useChat.js';
import { MessageList } from './MessageList.js';
import { InputBox } from './InputBox.js';
import { LoadingSpinner } from './LoadingSpinner.js';
import { ToolApproval } from './ToolApproval.js';
import { useToolExecution } from '../hooks/useToolExecution.js';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  isGenerating: boolean;
  onSendMessage: (message: string, approvalCallback?: (request: any) => Promise<any>) => Promise<void>;
  onClear: () => void;
  config: Config;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  isGenerating,
  onSendMessage,
  onClear,
  config,
}) => {
  const [inputValue, setInputValue] = useState('');

  // Tool execution integration
  const {
    pendingApproval,
    approveTool,
    denyTool,
    approveAllTools,
    denyAllTools,
    getApprovalCallback,
  } = useToolExecution(config);

  const handleSubmit = useCallback(
    async (message: string) => {
      if (!message.trim() || isGenerating) {
        return;
      }

      // Handle built-in commands
      const trimmedMessage = message.trim();

      if (trimmedMessage === '/clear') {
        onClear();
        return;
      }

      if (trimmedMessage === '/help') {
        // Add help message to chat
        const helpMessage = `
Available commands:
- /clear - Clear chat history
- /help - Show this help message
- /quit - Exit the application

Keyboard shortcuts:
- Ctrl+C - Exit
- Ctrl+L - Clear chat
- Ctrl+H - Toggle help
- Tab - Autocomplete (when available)
- ↑/↓ - Navigate command history
      `.trim();

        // This would ideally be handled by adding a system message
        // For now, we'll just send it as a regular message
        await onSendMessage(`Help:\n${helpMessage}`, getApprovalCallback());
        return;
      }

      if (trimmedMessage === '/quit') {
        process.exit(0);
      }

      // Send the message with tool approval callback
      await onSendMessage(message, getApprovalCallback());
      setInputValue('');
    },
    [isGenerating, onSendMessage, onClear, getApprovalCallback],
  );

  const handleInputChange = useCallback((value: string) => {
    setInputValue(value);
  }, []);

  // Show tool approval screen if pending
  if (pendingApproval) {
    return (
      <ToolApproval
        request={pendingApproval}
        onApprove={approveTool}
        onDeny={denyTool}
        onApproveAll={approveAllTools}
        onDenyAll={denyAllTools}
      />
    );
  }

  return (
    <Box flexDirection="column" height="100%">
      {/* Messages Area */}
      <Box flexGrow={1} flexDirection="column" minHeight={0}>
        <MessageList messages={messages} />

        {/* Loading indicator */}
        {isGenerating && (
          <Box paddingX={2} paddingY={1}>
            <LoadingSpinner
              text="Generating response"
              color="yellow"
              type="dots"
            />
          </Box>
        )}
      </Box>

      {/* Input Area */}
      <Box flexShrink={0}>
        <InputBox
          value={inputValue}
          onChange={handleInputChange}
          onSubmit={handleSubmit}
          disabled={isGenerating}
          placeholder={
            isGenerating ? 'Generating response...' : 'Type your message...'
          }
        />
      </Box>
    </Box>
  );
};
