/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import {
  Tool
} from "./tools.js";
import { getErrorMessage } from "../utils/errors.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
class MCPClientTool extends Tool {
  static Name = "mcp-client";
  servers = /* @__PURE__ */ new Map();
  constructor() {
    super(
      MCPClientTool.Name,
      "Manage Model Context Protocol (MCP) server connections and tool calls"
    );
  }
  getDefinition() {
    return {
      name: this.name,
      description: this.description,
      inputSchema: {
        type: "object",
        properties: {
          action: {
            type: "string",
            enum: ["list", "connect", "disconnect", "call"],
            description: "The MCP action to perform"
          },
          server_name: {
            type: "string",
            description: "Name of the MCP server (required for connect, disconnect, call)"
          },
          server_command: {
            type: "string",
            description: "Command to start the MCP server (required for connect)"
          },
          tool_name: {
            type: "string",
            description: "Name of the tool to call (required for call action)"
          },
          tool_params: {
            type: "object",
            description: "Parameters to pass to the tool (for call action)",
            additionalProperties: true
          }
        },
        required: ["action"]
      }
    };
  }
  async connectToServer(name, command, args = [], env = {}) {
    try {
      const commandParts = command.split(" ");
      const executable = commandParts[0];
      const commandArgs = [...commandParts.slice(1), ...args];
      const cleanEnv = {};
      for (const [key, value] of Object.entries({ ...process.env, ...env })) {
        if (value !== void 0) {
          cleanEnv[key] = value;
        }
      }
      const transport = new StdioClientTransport({
        command: executable,
        args: commandArgs,
        env: cleanEnv
      });
      const client = new Client(
        {
          name: "arien-cli",
          version: "0.1.0"
        },
        {
          capabilities: {
            tools: {},
            resources: {},
            prompts: {}
          }
        }
      );
      await client.connect(transport);
      const toolsResponse = await client.listTools();
      const toolNames = toolsResponse.tools.map((tool) => tool.name);
      const server = {
        name,
        command,
        args,
        env,
        connected: true,
        tools: toolNames,
        client,
        transport
      };
      this.servers.set(name, server);
      return server;
    } catch (error) {
      const server = {
        name,
        command,
        args,
        env,
        connected: false,
        tools: [],
        lastError: getErrorMessage(error)
      };
      this.servers.set(name, server);
      throw error;
    }
  }
  async disconnectFromServer(name) {
    const server = this.servers.get(name);
    if (server) {
      try {
        if (server.transport) {
          await server.transport.close();
        }
      } catch (error) {
        server.lastError = getErrorMessage(error);
      }
      server.connected = false;
      server.tools = [];
      server.client = void 0;
      server.transport = void 0;
    }
  }
  async callServerTool(serverName, toolName, params) {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server not found: ${serverName}`);
    }
    if (!server.connected || !server.client) {
      throw new Error(`Server not connected: ${serverName}`);
    }
    if (!server.tools.includes(toolName)) {
      throw new Error(`Tool not available: ${toolName}`);
    }
    try {
      const result = await server.client.callTool({
        name: toolName,
        arguments: params || {}
      });
      return {
        result: result.content,
        toolName,
        serverName,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      throw new Error(`Tool call failed: ${getErrorMessage(error)}`);
    }
  }
  formatServerList() {
    if (this.servers.size === 0) {
      return "No MCP servers configured.";
    }
    let output = `MCP Servers (${this.servers.size}):

`;
    for (const [name, server] of this.servers) {
      const status = server.connected ? "\u{1F7E2} Connected" : "\u{1F534} Disconnected";
      output += `\u{1F4E1} ${name}
`;
      output += `   Status: ${status}
`;
      output += `   Command: ${server.command}
`;
      output += `   Tools: ${server.tools.length > 0 ? server.tools.join(", ") : "None"}
`;
      if (server.lastError) {
        output += `   Last Error: ${server.lastError}
`;
      }
      output += "\n";
    }
    return output.trim();
  }
  async execute(params, context) {
    try {
      switch (params.action) {
        case "list": {
          const output = this.formatServerList();
          return this.createSuccessResult(output, {
            serverCount: this.servers.size,
            connectedServers: Array.from(this.servers.values()).filter(
              (s) => s.connected
            ).length
          });
        }
        case "connect": {
          if (!params.server_name) {
            return this.createErrorResult(
              "Server name is required for connect action"
            );
          }
          if (!params.server_command) {
            return this.createErrorResult(
              "Server command is required for connect action"
            );
          }
          const server = await this.connectToServer(
            params.server_name,
            params.server_command,
            [],
            // args - could be extended to accept from params
            {}
            // env - could be extended to accept from params
          );
          return this.createSuccessResult(
            `Connected to MCP server "${params.server_name}"
Available tools: ${server.tools.join(", ")}`,
            {
              serverName: server.name,
              connected: server.connected,
              toolCount: server.tools.length,
              tools: server.tools
            }
          );
        }
        case "disconnect": {
          if (!params.server_name) {
            return this.createErrorResult(
              "Server name is required for disconnect action"
            );
          }
          const server = this.servers.get(params.server_name);
          if (!server) {
            return this.createErrorResult(
              `Server not found: ${params.server_name}`
            );
          }
          await this.disconnectFromServer(params.server_name);
          return this.createSuccessResult(
            `Disconnected from MCP server "${params.server_name}"`
          );
        }
        case "call": {
          if (!params.server_name) {
            return this.createErrorResult(
              "Server name is required for call action"
            );
          }
          if (!params.tool_name) {
            return this.createErrorResult(
              "Tool name is required for call action"
            );
          }
          const result = await this.callServerTool(
            params.server_name,
            params.tool_name,
            params.tool_params || {}
          );
          let output = `Called tool "${params.tool_name}" on server "${params.server_name}"

`;
          output += `Result:
${JSON.stringify(result, null, 2)}`;
          return this.createSuccessResult(output, {
            serverName: params.server_name,
            toolName: params.tool_name,
            result
          });
        }
        default:
          return this.createErrorResult(`Unknown action: ${params.action}`);
      }
    } catch (error) {
      return this.createErrorResult(getErrorMessage(error));
    }
  }
}
export {
  MCPClientTool
};
//# sourceMappingURL=mcp-client.js.map
