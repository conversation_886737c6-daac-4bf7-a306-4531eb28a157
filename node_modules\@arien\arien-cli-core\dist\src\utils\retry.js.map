{"version": 3, "sources": ["../../../src/utils/retry.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { AuthType } from '../config/config.js';\n\nexport interface RetryOptions {\n  maxAttempts: number;\n  initialDelayMs: number;\n  maxDelayMs: number;\n  shouldRetry: (error: Error) => boolean;\n  onPersistent429?: (authType?: string) => Promise<string | null>;\n  authType?: string;\n}\n\nconst DEFAULT_RETRY_OPTIONS: RetryOptions = {\n  maxAttempts: 5,\n  initialDelayMs: 5000,\n  maxDelayMs: 30000, // 30 seconds\n  shouldRetry: defaultShouldRetry,\n};\n\n/**\n * Default predicate function to determine if a retry should be attempted.\n * Retries on 429 (Too Many Requests) and 5xx server errors.\n * @param error The error object.\n * @returns True if the error is a transient error, false otherwise.\n */\nfunction defaultShouldRetry(error: Error | unknown): boolean {\n  // Check for common transient error status codes either in message or a status property\n  if (error && typeof (error as { status?: number }).status === 'number') {\n    const status = (error as { status: number }).status;\n    if (status === 429 || (status >= 500 && status < 600)) {\n      return true;\n    }\n  }\n  if (error instanceof Error && error.message) {\n    if (error.message.includes('429')) return true;\n    if (error.message.match(/5\\d{2}/)) return true;\n  }\n  return false;\n}\n\n/**\n * Delays execution for a specified number of milliseconds.\n * @param ms The number of milliseconds to delay.\n * @returns A promise that resolves after the delay.\n */\nfunction delay(ms: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\n/**\n * Retries a function with exponential backoff and jitter.\n * @param fn The asynchronous function to retry.\n * @param options Optional retry configuration.\n * @returns A promise that resolves with the result of the function if successful.\n * @throws The last error encountered if all attempts fail.\n */\nexport async function retryWithBackoff<T>(\n  fn: () => Promise<T>,\n  options?: Partial<RetryOptions>,\n): Promise<T> {\n  const {\n    maxAttempts,\n    initialDelayMs,\n    maxDelayMs,\n    onPersistent429,\n    authType,\n    shouldRetry,\n  } = {\n    ...DEFAULT_RETRY_OPTIONS,\n    ...options,\n  };\n\n  let attempt = 0;\n  let currentDelay = initialDelayMs;\n  let consecutive429Count = 0;\n\n  while (attempt < maxAttempts) {\n    attempt++;\n    try {\n      return await fn();\n    } catch (error) {\n      const errorStatus = getErrorStatus(error);\n\n      // Track consecutive 429 errors\n      if (errorStatus === 429) {\n        consecutive429Count++;\n      } else {\n        consecutive429Count = 0;\n      }\n\n      // If we have persistent 429s and a fallback callback for OAuth\n      if (\n        consecutive429Count >= 2 &&\n        onPersistent429 &&\n        authType === AuthType.LOGIN_WITH_GOOGLE\n      ) {\n        try {\n          const fallbackModel = await onPersistent429(authType);\n          if (fallbackModel) {\n            // Reset attempt counter and try with new model\n            attempt = 0;\n            consecutive429Count = 0;\n            currentDelay = initialDelayMs;\n            // With the model updated, we continue to the next attempt\n            continue;\n          }\n        } catch (fallbackError) {\n          // If fallback fails, continue with original error\n          console.warn('Fallback to Flash model failed:', fallbackError);\n        }\n      }\n\n      // Check if we've exhausted retries or shouldn't retry\n      if (attempt >= maxAttempts || !shouldRetry(error as Error)) {\n        throw error;\n      }\n\n      const { delayDurationMs, errorStatus: delayErrorStatus } =\n        getDelayDurationAndStatus(error);\n\n      if (delayDurationMs > 0) {\n        // Respect Retry-After header if present and parsed\n        console.warn(\n          `Attempt ${attempt} failed with status ${delayErrorStatus ?? 'unknown'}. Retrying after explicit delay of ${delayDurationMs}ms...`,\n          error,\n        );\n        await delay(delayDurationMs);\n        // Reset currentDelay for next potential non-429 error, or if Retry-After is not present next time\n        currentDelay = initialDelayMs;\n      } else {\n        // Fallback to exponential backoff with jitter\n        logRetryAttempt(attempt, error, errorStatus);\n        // Add jitter: +/- 30% of currentDelay\n        const jitter = currentDelay * 0.3 * (Math.random() * 2 - 1);\n        const delayWithJitter = Math.max(0, currentDelay + jitter);\n        await delay(delayWithJitter);\n        currentDelay = Math.min(maxDelayMs, currentDelay * 2);\n      }\n    }\n  }\n  // This line should theoretically be unreachable due to the throw in the catch block.\n  // Added for type safety and to satisfy the compiler that a promise is always returned.\n  throw new Error('Retry attempts exhausted');\n}\n\n/**\n * Extracts the HTTP status code from an error object.\n * @param error The error object.\n * @returns The HTTP status code, or undefined if not found.\n */\nfunction getErrorStatus(error: unknown): number | undefined {\n  if (typeof error === 'object' && error !== null) {\n    if ('status' in error && typeof error.status === 'number') {\n      return error.status;\n    }\n    // Check for error.response.status (common in axios errors)\n    if (\n      'response' in error &&\n      typeof (error as { response?: unknown }).response === 'object' &&\n      (error as { response?: unknown }).response !== null\n    ) {\n      const response = (\n        error as { response: { status?: unknown; headers?: unknown } }\n      ).response;\n      if ('status' in response && typeof response.status === 'number') {\n        return response.status;\n      }\n    }\n  }\n  return undefined;\n}\n\n/**\n * Extracts the Retry-After delay from an error object's headers.\n * @param error The error object.\n * @returns The delay in milliseconds, or 0 if not found or invalid.\n */\nfunction getRetryAfterDelayMs(error: unknown): number {\n  if (typeof error === 'object' && error !== null) {\n    // Check for error.response.headers (common in axios errors)\n    if (\n      'response' in error &&\n      typeof (error as { response?: unknown }).response === 'object' &&\n      (error as { response?: unknown }).response !== null\n    ) {\n      const response = (error as { response: { headers?: unknown } }).response;\n      if (\n        'headers' in response &&\n        typeof response.headers === 'object' &&\n        response.headers !== null\n      ) {\n        const headers = response.headers as { 'retry-after'?: unknown };\n        const retryAfterHeader = headers['retry-after'];\n        if (typeof retryAfterHeader === 'string') {\n          const retryAfterSeconds = parseInt(retryAfterHeader, 10);\n          if (!isNaN(retryAfterSeconds)) {\n            return retryAfterSeconds * 1000;\n          }\n          // It might be an HTTP date\n          const retryAfterDate = new Date(retryAfterHeader);\n          if (!isNaN(retryAfterDate.getTime())) {\n            return Math.max(0, retryAfterDate.getTime() - Date.now());\n          }\n        }\n      }\n    }\n  }\n  return 0;\n}\n\n/**\n * Determines the delay duration based on the error, prioritizing Retry-After header.\n * @param error The error object.\n * @returns An object containing the delay duration in milliseconds and the error status.\n */\nfunction getDelayDurationAndStatus(error: unknown): {\n  delayDurationMs: number;\n  errorStatus: number | undefined;\n} {\n  const errorStatus = getErrorStatus(error);\n  let delayDurationMs = 0;\n\n  if (errorStatus === 429) {\n    delayDurationMs = getRetryAfterDelayMs(error);\n  }\n  return { delayDurationMs, errorStatus };\n}\n\n/**\n * Logs a message for a retry attempt when using exponential backoff.\n * @param attempt The current attempt number.\n * @param error The error that caused the retry.\n * @param errorStatus The HTTP status code of the error, if available.\n */\nfunction logRetryAttempt(\n  attempt: number,\n  error: unknown,\n  errorStatus?: number,\n): void {\n  let message = `Attempt ${attempt} failed. Retrying with backoff...`;\n  if (errorStatus) {\n    message = `Attempt ${attempt} failed with status ${errorStatus}. Retrying with backoff...`;\n  }\n\n  if (errorStatus === 429) {\n    console.warn(message, error);\n  } else if (errorStatus && errorStatus >= 500 && errorStatus < 600) {\n    console.error(message, error);\n  } else if (error instanceof Error) {\n    // Fallback for errors that might not have a status but have a message\n    if (error.message.includes('429')) {\n      console.warn(\n        `Attempt ${attempt} failed with 429 error (no Retry-After header). Retrying with backoff...`,\n        error,\n      );\n    } else if (error.message.match(/5\\d{2}/)) {\n      console.error(\n        `Attempt ${attempt} failed with 5xx error. Retrying with backoff...`,\n        error,\n      );\n    } else {\n      console.warn(message, error); // Default to warn for other errors\n    }\n  } else {\n    console.warn(message, error); // Default to warn if error type is unknown\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,gBAAgB;AAWzB,MAAM,wBAAsC;AAAA,EAC1C,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA;AAAA,EACZ,aAAa;AACf;AAQA,SAAS,mBAAmB,OAAiC;AAE3D,MAAI,SAAS,OAAQ,MAA8B,WAAW,UAAU;AACtE,UAAM,SAAU,MAA6B;AAC7C,QAAI,WAAW,OAAQ,UAAU,OAAO,SAAS,KAAM;AACrD,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,iBAAiB,SAAS,MAAM,SAAS;AAC3C,QAAI,MAAM,QAAQ,SAAS,KAAK,EAAG,QAAO;AAC1C,QAAI,MAAM,QAAQ,MAAM,QAAQ,EAAG,QAAO;AAAA,EAC5C;AACA,SAAO;AACT;AAOA,SAAS,MAAM,IAA2B;AACxC,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AACzD;AASA,eAAsB,iBACpB,IACA,SACY;AACZ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,MAAI,UAAU;AACd,MAAI,eAAe;AACnB,MAAI,sBAAsB;AAE1B,SAAO,UAAU,aAAa;AAC5B;AACA,QAAI;AACF,aAAO,MAAM,GAAG;AAAA,IAClB,SAAS,OAAO;AACd,YAAM,cAAc,eAAe,KAAK;AAGxC,UAAI,gBAAgB,KAAK;AACvB;AAAA,MACF,OAAO;AACL,8BAAsB;AAAA,MACxB;AAGA,UACE,uBAAuB,KACvB,mBACA,aAAa,SAAS,mBACtB;AACA,YAAI;AACF,gBAAM,gBAAgB,MAAM,gBAAgB,QAAQ;AACpD,cAAI,eAAe;AAEjB,sBAAU;AACV,kCAAsB;AACtB,2BAAe;AAEf;AAAA,UACF;AAAA,QACF,SAAS,eAAe;AAEtB,kBAAQ,KAAK,mCAAmC,aAAa;AAAA,QAC/D;AAAA,MACF;AAGA,UAAI,WAAW,eAAe,CAAC,YAAY,KAAc,GAAG;AAC1D,cAAM;AAAA,MACR;AAEA,YAAM,EAAE,iBAAiB,aAAa,iBAAiB,IACrD,0BAA0B,KAAK;AAEjC,UAAI,kBAAkB,GAAG;AAEvB,gBAAQ;AAAA,UACN,WAAW,OAAO,uBAAuB,oBAAoB,SAAS,sCAAsC,eAAe;AAAA,UAC3H;AAAA,QACF;AACA,cAAM,MAAM,eAAe;AAE3B,uBAAe;AAAA,MACjB,OAAO;AAEL,wBAAgB,SAAS,OAAO,WAAW;AAE3C,cAAM,SAAS,eAAe,OAAO,KAAK,OAAO,IAAI,IAAI;AACzD,cAAM,kBAAkB,KAAK,IAAI,GAAG,eAAe,MAAM;AACzD,cAAM,MAAM,eAAe;AAC3B,uBAAe,KAAK,IAAI,YAAY,eAAe,CAAC;AAAA,MACtD;AAAA,IACF;AAAA,EACF;AAGA,QAAM,IAAI,MAAM,0BAA0B;AAC5C;AAOA,SAAS,eAAe,OAAoC;AAC1D,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC/C,QAAI,YAAY,SAAS,OAAO,MAAM,WAAW,UAAU;AACzD,aAAO,MAAM;AAAA,IACf;AAEA,QACE,cAAc,SACd,OAAQ,MAAiC,aAAa,YACrD,MAAiC,aAAa,MAC/C;AACA,YAAM,WACJ,MACA;AACF,UAAI,YAAY,YAAY,OAAO,SAAS,WAAW,UAAU;AAC/D,eAAO,SAAS;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,qBAAqB,OAAwB;AACpD,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAE/C,QACE,cAAc,SACd,OAAQ,MAAiC,aAAa,YACrD,MAAiC,aAAa,MAC/C;AACA,YAAM,WAAY,MAA8C;AAChE,UACE,aAAa,YACb,OAAO,SAAS,YAAY,YAC5B,SAAS,YAAY,MACrB;AACA,cAAM,UAAU,SAAS;AACzB,cAAM,mBAAmB,QAAQ,aAAa;AAC9C,YAAI,OAAO,qBAAqB,UAAU;AACxC,gBAAM,oBAAoB,SAAS,kBAAkB,EAAE;AACvD,cAAI,CAAC,MAAM,iBAAiB,GAAG;AAC7B,mBAAO,oBAAoB;AAAA,UAC7B;AAEA,gBAAM,iBAAiB,IAAI,KAAK,gBAAgB;AAChD,cAAI,CAAC,MAAM,eAAe,QAAQ,CAAC,GAAG;AACpC,mBAAO,KAAK,IAAI,GAAG,eAAe,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,UAC1D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,0BAA0B,OAGjC;AACA,QAAM,cAAc,eAAe,KAAK;AACxC,MAAI,kBAAkB;AAEtB,MAAI,gBAAgB,KAAK;AACvB,sBAAkB,qBAAqB,KAAK;AAAA,EAC9C;AACA,SAAO,EAAE,iBAAiB,YAAY;AACxC;AAQA,SAAS,gBACP,SACA,OACA,aACM;AACN,MAAI,UAAU,WAAW,OAAO;AAChC,MAAI,aAAa;AACf,cAAU,WAAW,OAAO,uBAAuB,WAAW;AAAA,EAChE;AAEA,MAAI,gBAAgB,KAAK;AACvB,YAAQ,KAAK,SAAS,KAAK;AAAA,EAC7B,WAAW,eAAe,eAAe,OAAO,cAAc,KAAK;AACjE,YAAQ,MAAM,SAAS,KAAK;AAAA,EAC9B,WAAW,iBAAiB,OAAO;AAEjC,QAAI,MAAM,QAAQ,SAAS,KAAK,GAAG;AACjC,cAAQ;AAAA,QACN,WAAW,OAAO;AAAA,QAClB;AAAA,MACF;AAAA,IACF,WAAW,MAAM,QAAQ,MAAM,QAAQ,GAAG;AACxC,cAAQ;AAAA,QACN,WAAW,OAAO;AAAA,QAClB;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,SAAS,KAAK;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,YAAQ,KAAK,SAAS,KAAK;AAAA,EAC7B;AACF;", "names": []}