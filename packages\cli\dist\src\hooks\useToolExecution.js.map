{"version": 3, "sources": ["../../../src/hooks/useToolExecution.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { useState, useCallback, useRef } from 'react';\nimport { \n  Config, \n  ToolApprovalRequest, \n  ToolApproval,\n  ToolParams,\n  ToolResult,\n  ToolExecutionContext,\n  ApprovalMode\n} from '@arien/arien-cli-core';\n\nexport interface UseToolExecutionReturn {\n  pendingApproval: ToolApprovalRequest | null;\n  approveTool: () => void;\n  denyTool: (reason?: string) => void;\n  approveAllTools: () => void;\n  denyAllTools: () => void;\n  getApprovalCallback: () => (request: ToolApprovalRequest) => Promise<ToolApproval>;\n}\n\nexport const useToolExecution = (config: Config): UseToolExecutionReturn => {\n  const [pendingApproval, setPendingApproval] = useState<ToolApprovalRequest | null>(null);\n  const [approvalMode, setApprovalMode] = useState<ApprovalMode>(config.getApprovalMode());\n  \n  // Store the resolve function for the current approval promise\n  const approvalResolveRef = useRef<((approval: ToolApproval) => void) | null>(null);\n  \n  // Track approved/denied tools for \"once\" mode\n  const approvedToolsRef = useRef<Set<string>>(new Set());\n  const deniedToolsRef = useRef<Set<string>>(new Set());\n\n  const approveTool = useCallback(() => {\n    if (pendingApproval && approvalResolveRef.current) {\n      // Remember approval for \"once\" mode\n      if (approvalMode === ApprovalMode.ONCE) {\n        approvedToolsRef.current.add(pendingApproval.toolName);\n      }\n      \n      approvalResolveRef.current({\n        approved: true,\n        rememberChoice: approvalMode === ApprovalMode.ONCE,\n      });\n      \n      setPendingApproval(null);\n      approvalResolveRef.current = null;\n    }\n  }, [pendingApproval, approvalMode]);\n\n  const denyTool = useCallback((reason?: string) => {\n    if (pendingApproval && approvalResolveRef.current) {\n      // Remember denial for \"once\" mode\n      if (approvalMode === ApprovalMode.ONCE) {\n        deniedToolsRef.current.add(pendingApproval.toolName);\n      }\n      \n      approvalResolveRef.current({\n        approved: false,\n        reason: reason || 'Tool execution denied by user',\n        rememberChoice: approvalMode === ApprovalMode.ONCE,\n      });\n      \n      setPendingApproval(null);\n      approvalResolveRef.current = null;\n    }\n  }, [pendingApproval, approvalMode]);\n\n  const approveAllTools = useCallback(() => {\n    if (pendingApproval && approvalResolveRef.current) {\n      // Set approval mode to always\n      setApprovalMode(ApprovalMode.ALWAYS);\n      config.setApprovalMode(ApprovalMode.ALWAYS);\n      \n      approvalResolveRef.current({\n        approved: true,\n        rememberChoice: true,\n      });\n      \n      setPendingApproval(null);\n      approvalResolveRef.current = null;\n    }\n  }, [pendingApproval, config]);\n\n  const denyAllTools = useCallback(() => {\n    if (pendingApproval && approvalResolveRef.current) {\n      // Set approval mode to never\n      setApprovalMode(ApprovalMode.NEVER);\n      config.setApprovalMode(ApprovalMode.NEVER);\n      \n      approvalResolveRef.current({\n        approved: false,\n        reason: 'All tool execution denied by user',\n        rememberChoice: true,\n      });\n      \n      setPendingApproval(null);\n      approvalResolveRef.current = null;\n    }\n  }, [pendingApproval, config]);\n\n  const getApprovalCallback = useCallback(() => {\n    return async (request: ToolApprovalRequest): Promise<ToolApproval> => {\n      // Check approval mode\n      const currentMode = config.getApprovalMode();\n      \n      // Always approve\n      if (currentMode === ApprovalMode.ALWAYS) {\n        return {\n          approved: true,\n          rememberChoice: true,\n        };\n      }\n      \n      // Never approve\n      if (currentMode === ApprovalMode.NEVER) {\n        return {\n          approved: false,\n          reason: 'Tool execution disabled by approval mode',\n          rememberChoice: true,\n        };\n      }\n      \n      // Once mode - check if we've already decided on this tool\n      if (currentMode === ApprovalMode.ONCE) {\n        if (approvedToolsRef.current.has(request.toolName)) {\n          return {\n            approved: true,\n            rememberChoice: true,\n          };\n        }\n        \n        if (deniedToolsRef.current.has(request.toolName)) {\n          return {\n            approved: false,\n            reason: 'Tool execution previously denied',\n            rememberChoice: true,\n          };\n        }\n      }\n      \n      // Need user approval - show UI and wait for response\n      return new Promise<ToolApproval>((resolve) => {\n        setPendingApproval(request);\n        approvalResolveRef.current = resolve;\n      });\n    };\n  }, [config]);\n\n  return {\n    pendingApproval,\n    approveTool,\n    denyTool,\n    approveAllTools,\n    denyAllTools,\n    getApprovalCallback,\n  };\n};\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,UAAU,aAAa,cAAc;AAC9C;AAAA,EAOE;AAAA,OACK;AAWA,MAAM,mBAAmB,CAAC,WAA2C;AAC1E,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,SAAqC,IAAI;AACvF,QAAM,CAAC,cAAc,eAAe,IAAI,SAAuB,OAAO,gBAAgB,CAAC;AAGvF,QAAM,qBAAqB,OAAkD,IAAI;AAGjF,QAAM,mBAAmB,OAAoB,oBAAI,IAAI,CAAC;AACtD,QAAM,iBAAiB,OAAoB,oBAAI,IAAI,CAAC;AAEpD,QAAM,cAAc,YAAY,MAAM;AACpC,QAAI,mBAAmB,mBAAmB,SAAS;AAEjD,UAAI,iBAAiB,aAAa,MAAM;AACtC,yBAAiB,QAAQ,IAAI,gBAAgB,QAAQ;AAAA,MACvD;AAEA,yBAAmB,QAAQ;AAAA,QACzB,UAAU;AAAA,QACV,gBAAgB,iBAAiB,aAAa;AAAA,MAChD,CAAC;AAED,yBAAmB,IAAI;AACvB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,iBAAiB,YAAY,CAAC;AAElC,QAAM,WAAW,YAAY,CAAC,WAAoB;AAChD,QAAI,mBAAmB,mBAAmB,SAAS;AAEjD,UAAI,iBAAiB,aAAa,MAAM;AACtC,uBAAe,QAAQ,IAAI,gBAAgB,QAAQ;AAAA,MACrD;AAEA,yBAAmB,QAAQ;AAAA,QACzB,UAAU;AAAA,QACV,QAAQ,UAAU;AAAA,QAClB,gBAAgB,iBAAiB,aAAa;AAAA,MAChD,CAAC;AAED,yBAAmB,IAAI;AACvB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,iBAAiB,YAAY,CAAC;AAElC,QAAM,kBAAkB,YAAY,MAAM;AACxC,QAAI,mBAAmB,mBAAmB,SAAS;AAEjD,sBAAgB,aAAa,MAAM;AACnC,aAAO,gBAAgB,aAAa,MAAM;AAE1C,yBAAmB,QAAQ;AAAA,QACzB,UAAU;AAAA,QACV,gBAAgB;AAAA,MAClB,CAAC;AAED,yBAAmB,IAAI;AACvB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAE5B,QAAM,eAAe,YAAY,MAAM;AACrC,QAAI,mBAAmB,mBAAmB,SAAS;AAEjD,sBAAgB,aAAa,KAAK;AAClC,aAAO,gBAAgB,aAAa,KAAK;AAEzC,yBAAmB,QAAQ;AAAA,QACzB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,gBAAgB;AAAA,MAClB,CAAC;AAED,yBAAmB,IAAI;AACvB,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,iBAAiB,MAAM,CAAC;AAE5B,QAAM,sBAAsB,YAAY,MAAM;AAC5C,WAAO,OAAO,YAAwD;AAEpE,YAAM,cAAc,OAAO,gBAAgB;AAG3C,UAAI,gBAAgB,aAAa,QAAQ;AACvC,eAAO;AAAA,UACL,UAAU;AAAA,UACV,gBAAgB;AAAA,QAClB;AAAA,MACF;AAGA,UAAI,gBAAgB,aAAa,OAAO;AACtC,eAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,MACF;AAGA,UAAI,gBAAgB,aAAa,MAAM;AACrC,YAAI,iBAAiB,QAAQ,IAAI,QAAQ,QAAQ,GAAG;AAClD,iBAAO;AAAA,YACL,UAAU;AAAA,YACV,gBAAgB;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,eAAe,QAAQ,IAAI,QAAQ,QAAQ,GAAG;AAChD,iBAAO;AAAA,YACL,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAGA,aAAO,IAAI,QAAsB,CAAC,YAAY;AAC5C,2BAAmB,OAAO;AAC1B,2BAAmB,UAAU;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AAEX,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}