{"version": 3, "sources": ["../../src/arien.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React from 'react';\nimport { render } from 'ink';\nimport { AppWrapper } from './ui/App.js';\nimport { loadCliConfig } from './config/config.js';\nimport { readStdin } from './utils/readStdin.js';\nimport { basename } from 'node:path';\nimport v8 from 'node:v8';\nimport os from 'node:os';\nimport { spawn } from 'node:child_process';\nimport { start_sandbox } from './utils/sandbox.js';\nimport {\n  SettingScope,\n  USER_SETTINGS_PATH,\n} from './config/settings.js';\nimport { themeManager } from './ui/themes/theme-manager.js';\nimport { getStartupWarnings } from './utils/startupWarnings.js';\nimport { runNonInteractive } from './nonInteractiveCli.js';\nimport { loadExtensions, Extension } from './config/extension.js';\nimport { cleanupCheckpoints } from './utils/cleanup.js';\nimport {\n  ApprovalMode,\n  Config,\n  EditTool,\n  ShellTool,\n  WriteFileTool,\n  sessionId,\n  logUserPrompt,\n  AuthType,\n} from '@arien/arien-cli-core';\nimport { validateAuthMethod } from './config/auth.js';\nimport { setMaxSizedBoxDebugging } from './ui/components/shared/MaxSizedBox.js';\n\nfunction getNodeMemoryArgs(config: Config): string[] {\n  const totalMemoryMB = os.totalmem() / (1024 * 1024);\n  const heapStats = v8.getHeapStatistics();\n  const currentMaxOldSpaceSizeMb = Math.floor(\n    heapStats.heap_size_limit / 1024 / 1024,\n  );\n\n  // Set target to 50% of total memory\n  const targetMaxOldSpaceSizeInMB = Math.floor(totalMemoryMB * 0.5);\n  if (config.getDebugMode()) {\n    console.debug(\n      `Current heap size ${currentMaxOldSpaceSizeMb.toFixed(2)} MB`,\n    );\n  }\n\n  if (process.env.ARIEN_CLI_NO_RELAUNCH) {\n    return [];\n  }\n\n  if (targetMaxOldSpaceSizeInMB > currentMaxOldSpaceSizeMb) {\n    if (config.getDebugMode()) {\n      console.debug(\n        `Need to relaunch with more memory: ${targetMaxOldSpaceSizeInMB.toFixed(2)} MB`,\n      );\n    }\n    return [`--max-old-space-size=${targetMaxOldSpaceSizeInMB}`];\n  }\n\n  return [];\n}\n\nasync function relaunchWithAdditionalArgs(additionalArgs: string[]) {\n  const nodeArgs = [...additionalArgs, ...process.argv.slice(1)];\n  const newEnv = { ...process.env, ARIEN_CLI_NO_RELAUNCH: 'true' };\n\n  const child = spawn(process.execPath, nodeArgs, {\n    stdio: 'inherit',\n    env: newEnv,\n  });\n\n  await new Promise((resolve) => child.on('close', resolve));\n  process.exit(0);\n}\n\nexport async function main() {\n  const workspaceRoot = process.cwd();\n\n  // Create unified config with settings loading\n  const extensions = loadExtensions(workspaceRoot);\n  const config = await loadCliConfig({} as any, extensions, sessionId);\n\n  await cleanupCheckpoints();\n\n  // Check for settings errors from the unified config\n  const settingsErrors = config.getSettingsErrors();\n  if (settingsErrors.length > 0) {\n    for (const error of settingsErrors) {\n      let errorMessage = `Error in ${error.path}: ${error.message}`;\n      if (!process.env.NO_COLOR) {\n        errorMessage = `\\x1b[31m${errorMessage}\\x1b[0m`;\n      }\n      console.error(errorMessage);\n      console.error(`Please fix ${error.path} and try again.`);\n    }\n    process.exit(1);\n  }\n\n  // set default fallback to gemini api key\n  // this has to go after load cli because thats where the env is set\n  if (!config.getSelectedAuthType() && process.env.GEMINI_API_KEY) {\n    config.setSelectedAuthType(AuthType.USE_GEMINI);\n  }\n\n  setMaxSizedBoxDebugging(config.getDebugMode());\n\n  // Initialize centralized FileDiscoveryService\n  config.getFileService();\n  if (config.getCheckpointingEnabled()) {\n    try {\n      await config.getGitService();\n    } catch {\n      // For now swallow the error, later log it.\n    }\n  }\n\n  // Initialize MCP servers\n  try {\n    await config.initializeMCPServers();\n  } catch (error) {\n    console.warn('Warning: Failed to initialize MCP servers:', error);\n    // Continue execution even if MCP servers fail to start\n  }\n\n  const theme = config.getTheme();\n  if (theme) {\n    if (!themeManager.setActiveTheme(theme)) {\n      // If the theme is not found during initial load, log a warning and continue.\n      // The useThemeCommand hook in App.tsx will handle opening the dialog.\n      console.warn(`Warning: Theme \"${theme}\" not found.`);\n    }\n  }\n\n  const memoryArgs = config.getAutoConfigureMaxOldSpaceSize()\n    ? getNodeMemoryArgs(config)\n    : [];\n\n  // hop into sandbox if we are outside and sandboxing is enabled\n  if (!process.env.SANDBOX) {\n    const sandboxConfig = config.getSandbox();\n    if (sandboxConfig) {\n      const selectedAuthType = config.getSelectedAuthType();\n      if (selectedAuthType) {\n        // Validate authentication here because the sandbox will interfere with the Oauth2 web redirect.\n        try {\n          const err = validateAuthMethod(selectedAuthType);\n          if (err) {\n            throw new Error(err);\n          }\n          await config.refreshAuth(selectedAuthType);\n        } catch (err) {\n          console.error('Error authenticating:', err);\n          process.exit(1);\n        }\n      }\n      await start_sandbox(sandboxConfig, memoryArgs);\n      process.exit(0);\n    } else {\n      // Not in a sandbox and not entering one, so relaunch with additional\n      // arguments to control memory usage if needed.\n      if (memoryArgs.length > 0) {\n        await relaunchWithAdditionalArgs(memoryArgs);\n        process.exit(0);\n      }\n    }\n  }\n  let input = config.getQuestion();\n  const startupWarnings = await getStartupWarnings();\n\n  // Render UI, passing necessary config values. Check that there is no command line question.\n  if (process.stdin.isTTY && input?.length === 0) {\n    setWindowTitle(basename(workspaceRoot), config);\n    render(\n      <React.StrictMode>\n        <AppWrapper\n          config={config}\n          settings={config}\n          startupWarnings={startupWarnings}\n        />\n      </React.StrictMode>,\n      { exitOnCtrlC: false },\n    );\n    return;\n  }\n  // If not a TTY, read from stdin\n  // This is for cases where the user pipes input directly into the command\n  if (!process.stdin.isTTY) {\n    input += await readStdin();\n  }\n  if (!input) {\n    console.error('No input provided via stdin.');\n    process.exit(1);\n  }\n\n  logUserPrompt(config, {\n    'event.name': 'user_prompt',\n    'event.timestamp': new Date().toISOString(),\n    prompt: input,\n    prompt_length: input.length,\n  });\n\n  // Non-interactive mode handled by runNonInteractive\n  const nonInteractiveConfig = await loadNonInteractiveConfig(\n    config,\n    extensions,\n  );\n\n  await runNonInteractive(nonInteractiveConfig, input);\n  process.exit(0);\n}\n\nfunction setWindowTitle(title: string, config: Config) {\n  if (!config.getHideWindowTitle()) {\n    process.stdout.write(`\\x1b]2; Arien - ${title} \\x07`);\n\n    process.on('exit', () => {\n      process.stdout.write(`\\x1b]2;\\x07`);\n    });\n  }\n}\n\n// --- Global Unhandled Rejection Handler ---\nprocess.on('unhandledRejection', (reason, _promise) => {\n  // Log other unexpected unhandled rejections as critical errors\n  console.error('=========================================');\n  console.error('CRITICAL: Unhandled Promise Rejection!');\n  console.error('=========================================');\n  console.error('Reason:', reason);\n  console.error('Stack trace may follow:');\n  if (!(reason instanceof Error)) {\n    console.error(reason);\n  }\n  // Exit for genuinely unhandled errors\n  process.exit(1);\n});\n\nasync function loadNonInteractiveConfig(\n  config: Config,\n  extensions: Extension[],\n) {\n  let finalConfig = config;\n  if (config.getApprovalMode() !== ApprovalMode.YOLO) {\n    // Everything is not allowed, ensure that only read-only tools are configured.\n    const existingExcludeTools = config.getExcludeTools() || [];\n    const interactiveTools = [\n      ShellTool.Name,\n      EditTool.Name,\n      WriteFileTool.Name,\n    ];\n\n    const newExcludeTools = [\n      ...new Set([...existingExcludeTools, ...interactiveTools]),\n    ];\n\n    // Create a new config with the read-only tools excluded\n    finalConfig = await loadCliConfig(\n      { excludeTools: newExcludeTools } as any,\n      extensions,\n      config.getSessionId(),\n    );\n  }\n\n  return await validateNonInterActiveAuth(\n    config.getSelectedAuthType(),\n    finalConfig,\n  );\n}\n\nasync function validateNonInterActiveAuth(\n  selectedAuthType: AuthType | undefined,\n  nonInteractiveConfig: Config,\n) {\n  // making a special case for the cli. many headless environments might not have a settings.json set\n  // so if GEMINI_API_KEY is set, we'll use that. However since the oauth things are interactive anyway, we'll\n  // still expect that exists\n  if (!selectedAuthType && !process.env.GEMINI_API_KEY) {\n    console.error(\n      `Please set an Auth method in your ${USER_SETTINGS_PATH} OR specify GEMINI_API_KEY env variable file before running`,\n    );\n    process.exit(1);\n  }\n\n  selectedAuthType = selectedAuthType || AuthType.USE_GEMINI;\n  const err = validateAuthMethod(selectedAuthType);\n  if (err != null) {\n    console.error(err);\n    process.exit(1);\n  }\n\n  await nonInteractiveConfig.refreshAuth(selectedAuthType);\n  return nonInteractiveConfig;\n}\n"], "mappings": "AAqLQ;AArLR;AAAA;AAAA;AAAA;AAAA;AAMA,OAAO,WAAW;AAClB,SAAS,cAAc;AACvB,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,OAAO,QAAQ;AACf,OAAO,QAAQ;AACf,SAAS,aAAa;AACtB,SAAS,qBAAqB;AAC9B;AAAA,EAEE;AAAA,OACK;AACP,SAAS,oBAAoB;AAC7B,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAClC,SAAS,sBAAiC;AAC1C,SAAS,0BAA0B;AACnC;AAAA,EACE;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,SAAS,0BAA0B;AACnC,SAAS,+BAA+B;AAExC,SAAS,kBAAkB,QAA0B;AACnD,QAAM,gBAAgB,GAAG,SAAS,KAAK,OAAO;AAC9C,QAAM,YAAY,GAAG,kBAAkB;AACvC,QAAM,2BAA2B,KAAK;AAAA,IACpC,UAAU,kBAAkB,OAAO;AAAA,EACrC;AAGA,QAAM,4BAA4B,KAAK,MAAM,gBAAgB,GAAG;AAChE,MAAI,OAAO,aAAa,GAAG;AACzB,YAAQ;AAAA,MACN,qBAAqB,yBAAyB,QAAQ,CAAC,CAAC;AAAA,IAC1D;AAAA,EACF;AAEA,MAAI,QAAQ,IAAI,uBAAuB;AACrC,WAAO,CAAC;AAAA,EACV;AAEA,MAAI,4BAA4B,0BAA0B;AACxD,QAAI,OAAO,aAAa,GAAG;AACzB,cAAQ;AAAA,QACN,sCAAsC,0BAA0B,QAAQ,CAAC,CAAC;AAAA,MAC5E;AAAA,IACF;AACA,WAAO,CAAC,wBAAwB,yBAAyB,EAAE;AAAA,EAC7D;AAEA,SAAO,CAAC;AACV;AAEA,eAAe,2BAA2B,gBAA0B;AAClE,QAAM,WAAW,CAAC,GAAG,gBAAgB,GAAG,QAAQ,KAAK,MAAM,CAAC,CAAC;AAC7D,QAAM,SAAS,EAAE,GAAG,QAAQ,KAAK,uBAAuB,OAAO;AAE/D,QAAM,QAAQ,MAAM,QAAQ,UAAU,UAAU;AAAA,IAC9C,OAAO;AAAA,IACP,KAAK;AAAA,EACP,CAAC;AAED,QAAM,IAAI,QAAQ,CAAC,YAAY,MAAM,GAAG,SAAS,OAAO,CAAC;AACzD,UAAQ,KAAK,CAAC;AAChB;AAEA,eAAsB,OAAO;AAC3B,QAAM,gBAAgB,QAAQ,IAAI;AAGlC,QAAM,aAAa,eAAe,aAAa;AAC/C,QAAM,SAAS,MAAM,cAAc,CAAC,GAAU,YAAY,SAAS;AAEnE,QAAM,mBAAmB;AAGzB,QAAM,iBAAiB,OAAO,kBAAkB;AAChD,MAAI,eAAe,SAAS,GAAG;AAC7B,eAAW,SAAS,gBAAgB;AAClC,UAAI,eAAe,YAAY,MAAM,IAAI,KAAK,MAAM,OAAO;AAC3D,UAAI,CAAC,QAAQ,IAAI,UAAU;AACzB,uBAAe,WAAW,YAAY;AAAA,MACxC;AACA,cAAQ,MAAM,YAAY;AAC1B,cAAQ,MAAM,cAAc,MAAM,IAAI,iBAAiB;AAAA,IACzD;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAIA,MAAI,CAAC,OAAO,oBAAoB,KAAK,QAAQ,IAAI,gBAAgB;AAC/D,WAAO,oBAAoB,SAAS,UAAU;AAAA,EAChD;AAEA,0BAAwB,OAAO,aAAa,CAAC;AAG7C,SAAO,eAAe;AACtB,MAAI,OAAO,wBAAwB,GAAG;AACpC,QAAI;AACF,YAAM,OAAO,cAAc;AAAA,IAC7B,QAAQ;AAAA,IAER;AAAA,EACF;AAGA,MAAI;AACF,UAAM,OAAO,qBAAqB;AAAA,EACpC,SAAS,OAAO;AACd,YAAQ,KAAK,8CAA8C,KAAK;AAAA,EAElE;AAEA,QAAM,QAAQ,OAAO,SAAS;AAC9B,MAAI,OAAO;AACT,QAAI,CAAC,aAAa,eAAe,KAAK,GAAG;AAGvC,cAAQ,KAAK,mBAAmB,KAAK,cAAc;AAAA,IACrD;AAAA,EACF;AAEA,QAAM,aAAa,OAAO,gCAAgC,IACtD,kBAAkB,MAAM,IACxB,CAAC;AAGL,MAAI,CAAC,QAAQ,IAAI,SAAS;AACxB,UAAM,gBAAgB,OAAO,WAAW;AACxC,QAAI,eAAe;AACjB,YAAM,mBAAmB,OAAO,oBAAoB;AACpD,UAAI,kBAAkB;AAEpB,YAAI;AACF,gBAAM,MAAM,mBAAmB,gBAAgB;AAC/C,cAAI,KAAK;AACP,kBAAM,IAAI,MAAM,GAAG;AAAA,UACrB;AACA,gBAAM,OAAO,YAAY,gBAAgB;AAAA,QAC3C,SAAS,KAAK;AACZ,kBAAQ,MAAM,yBAAyB,GAAG;AAC1C,kBAAQ,KAAK,CAAC;AAAA,QAChB;AAAA,MACF;AACA,YAAM,cAAc,eAAe,UAAU;AAC7C,cAAQ,KAAK,CAAC;AAAA,IAChB,OAAO;AAGL,UAAI,WAAW,SAAS,GAAG;AACzB,cAAM,2BAA2B,UAAU;AAC3C,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,OAAO,YAAY;AAC/B,QAAM,kBAAkB,MAAM,mBAAmB;AAGjD,MAAI,QAAQ,MAAM,SAAS,OAAO,WAAW,GAAG;AAC9C,mBAAe,SAAS,aAAa,GAAG,MAAM;AAC9C;AAAA,MACE,oBAAC,MAAM,YAAN,EACC;AAAA,QAAC;AAAA;AAAA,UACC;AAAA,UACA,UAAU;AAAA,UACV;AAAA;AAAA,MACF,GACF;AAAA,MACA,EAAE,aAAa,MAAM;AAAA,IACvB;AACA;AAAA,EACF;AAGA,MAAI,CAAC,QAAQ,MAAM,OAAO;AACxB,aAAS,MAAM,UAAU;AAAA,EAC3B;AACA,MAAI,CAAC,OAAO;AACV,YAAQ,MAAM,8BAA8B;AAC5C,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,gBAAc,QAAQ;AAAA,IACpB,cAAc;AAAA,IACd,oBAAmB,oBAAI,KAAK,GAAE,YAAY;AAAA,IAC1C,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACvB,CAAC;AAGD,QAAM,uBAAuB,MAAM;AAAA,IACjC;AAAA,IACA;AAAA,EACF;AAEA,QAAM,kBAAkB,sBAAsB,KAAK;AACnD,UAAQ,KAAK,CAAC;AAChB;AAEA,SAAS,eAAe,OAAe,QAAgB;AACrD,MAAI,CAAC,OAAO,mBAAmB,GAAG;AAChC,YAAQ,OAAO,MAAM,mBAAmB,KAAK,OAAO;AAEpD,YAAQ,GAAG,QAAQ,MAAM;AACvB,cAAQ,OAAO,MAAM,aAAa;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAGA,QAAQ,GAAG,sBAAsB,CAAC,QAAQ,aAAa;AAErD,UAAQ,MAAM,2CAA2C;AACzD,UAAQ,MAAM,wCAAwC;AACtD,UAAQ,MAAM,2CAA2C;AACzD,UAAQ,MAAM,WAAW,MAAM;AAC/B,UAAQ,MAAM,yBAAyB;AACvC,MAAI,EAAE,kBAAkB,QAAQ;AAC9B,YAAQ,MAAM,MAAM;AAAA,EACtB;AAEA,UAAQ,KAAK,CAAC;AAChB,CAAC;AAED,eAAe,yBACb,QACA,YACA;AACA,MAAI,cAAc;AAClB,MAAI,OAAO,gBAAgB,MAAM,aAAa,MAAM;AAElD,UAAM,uBAAuB,OAAO,gBAAgB,KAAK,CAAC;AAC1D,UAAM,mBAAmB;AAAA,MACvB,UAAU;AAAA,MACV,SAAS;AAAA,MACT,cAAc;AAAA,IAChB;AAEA,UAAM,kBAAkB;AAAA,MACtB,GAAG,oBAAI,IAAI,CAAC,GAAG,sBAAsB,GAAG,gBAAgB,CAAC;AAAA,IAC3D;AAGA,kBAAc,MAAM;AAAA,MAClB,EAAE,cAAc,gBAAgB;AAAA,MAChC;AAAA,MACA,OAAO,aAAa;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,MAAM;AAAA,IACX,OAAO,oBAAoB;AAAA,IAC3B;AAAA,EACF;AACF;AAEA,eAAe,2BACb,kBACA,sBACA;AAIA,MAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,gBAAgB;AACpD,YAAQ;AAAA,MACN,qCAAqC,kBAAkB;AAAA,IACzD;AACA,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,qBAAmB,oBAAoB,SAAS;AAChD,QAAM,MAAM,mBAAmB,gBAAgB;AAC/C,MAAI,OAAO,MAAM;AACf,YAAQ,MAAM,GAAG;AACjB,YAAQ,KAAK,CAAC;AAAA,EAChB;AAEA,QAAM,qBAAqB,YAAY,gBAAgB;AACvD,SAAO;AACT;", "names": []}