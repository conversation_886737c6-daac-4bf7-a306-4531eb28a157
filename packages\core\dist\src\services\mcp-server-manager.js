/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { logger } from "../core/logger.js";
import { MCPToolFactory } from "../tools/mcp-tool.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { getErrorMessage } from "../utils/errors.js";
class MCPServerManager {
  config;
  toolRegistry;
  servers = /* @__PURE__ */ new Map();
  mcpToolFactory;
  initialized = false;
  constructor(config, toolRegistry) {
    this.config = config;
    this.toolRegistry = toolRegistry;
    this.mcpToolFactory = new MCPToolFactory(this.createToolCallHandler());
  }
  /**
   * Initialize MCP servers based on configuration
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    logger.debug("Initializing MCP Server Manager");
    const mcpServers = this.config.getMcpServers();
    const serverNames = Object.keys(mcpServers);
    if (serverNames.length === 0) {
      logger.debug("No MCP servers configured");
      this.initialized = true;
      return;
    }
    logger.info(`Starting ${serverNames.length} MCP servers: ${serverNames.join(", ")}`);
    const startupPromises = serverNames.map(async (serverName) => {
      const serverConfig = mcpServers[serverName];
      try {
        await this.startServer(serverName, serverConfig);
        logger.info(`Successfully started MCP server: ${serverName}`);
      } catch (error) {
        logger.error(`Failed to start MCP server ${serverName}:`, error);
      }
    });
    await Promise.allSettled(startupPromises);
    this.initialized = true;
    logger.debug("MCP Server Manager initialization complete");
  }
  /**
   * Start a single MCP server
   */
  async startServer(name, config) {
    const server = {
      name,
      config,
      connected: false,
      tools: [],
      retryCount: 0
    };
    this.servers.set(name, server);
    try {
      const transport = new StdioClientTransport({
        command: config.command,
        args: config.args || [],
        env: this.createServerEnvironment(config.env || {})
      });
      const client = new Client(
        {
          name: "arien-cli",
          version: "0.1.0"
        },
        {
          capabilities: {
            tools: {},
            resources: {},
            prompts: {}
          }
        }
      );
      await client.connect(transport);
      const toolsResponse = await client.listTools();
      const toolNames = toolsResponse.tools.map((tool) => tool.name);
      server.client = client;
      server.transport = transport;
      server.connected = true;
      server.tools = toolNames;
      await this.registerServerTools(name, toolsResponse.tools);
      logger.debug(`MCP server ${name} connected with ${toolNames.length} tools: ${toolNames.join(", ")}`);
    } catch (error) {
      server.lastError = getErrorMessage(error);
      server.connected = false;
      throw error;
    }
  }
  /**
   * Register tools from an MCP server with the tool registry
   */
  async registerServerTools(serverName, toolDefinitions) {
    for (const toolDef of toolDefinitions) {
      try {
        const mcpTool = this.mcpToolFactory.createTool(serverName, toolDef.name, toolDef);
        this.toolRegistry.registerMCPTool(mcpTool);
        logger.debug(`Registered MCP tool: ${mcpTool.name} from server: ${serverName}`);
      } catch (error) {
        logger.error(`Failed to register MCP tool ${toolDef.name} from server ${serverName}:`, error);
      }
    }
  }
  /**
   * Create environment for MCP server process
   */
  createServerEnvironment(serverEnv) {
    const cleanEnv = {};
    for (const [key, value] of Object.entries(process.env)) {
      if (value !== void 0) {
        cleanEnv[key] = value;
      }
    }
    for (const [key, value] of Object.entries(serverEnv)) {
      cleanEnv[key] = value;
    }
    return cleanEnv;
  }
  /**
   * Create tool call handler for MCP tools
   */
  createToolCallHandler() {
    return async (serverName, toolName, params) => {
      const server = this.servers.get(serverName);
      if (!server) {
        throw new Error(`MCP server not found: ${serverName}`);
      }
      if (!server.connected || !server.client) {
        throw new Error(`MCP server not connected: ${serverName}`);
      }
      try {
        const result = await server.client.callTool({
          name: toolName,
          arguments: params || {}
        });
        return result.content;
      } catch (error) {
        throw new Error(`MCP tool call failed: ${getErrorMessage(error)}`);
      }
    };
  }
  /**
   * Stop a specific MCP server
   */
  async stopServer(serverName) {
    const server = this.servers.get(serverName);
    if (!server) {
      return;
    }
    try {
      this.toolRegistry.unregisterMCPServerTools(serverName);
      if (server.transport) {
        await server.transport.close();
      }
      server.connected = false;
      server.client = void 0;
      server.transport = void 0;
      logger.debug(`Stopped MCP server: ${serverName}`);
    } catch (error) {
      logger.error(`Error stopping MCP server ${serverName}:`, error);
      server.lastError = getErrorMessage(error);
    }
  }
  /**
   * Stop all MCP servers
   */
  async stopAllServers() {
    const serverNames = Array.from(this.servers.keys());
    const stopPromises = serverNames.map((name) => this.stopServer(name));
    await Promise.allSettled(stopPromises);
    logger.debug("Stopped all MCP servers");
  }
  /**
   * Get status of all managed servers
   */
  getServerStatus() {
    const status = {};
    for (const [name, server] of this.servers) {
      status[name] = {
        connected: server.connected,
        tools: server.tools,
        lastError: server.lastError
      };
    }
    return status;
  }
  /**
   * Check if the manager is initialized
   */
  isInitialized() {
    return this.initialized;
  }
  /**
   * Get connected server count
   */
  getConnectedServerCount() {
    return Array.from(this.servers.values()).filter((server) => server.connected).length;
  }
}
export {
  MCPServerManager
};
//# sourceMappingURL=mcp-server-manager.js.map
