/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { AuthType } from "../config/config.js";
const DEFAULT_RETRY_OPTIONS = {
  maxAttempts: 5,
  initialDelayMs: 5e3,
  maxDelayMs: 3e4,
  // 30 seconds
  shouldRetry: defaultShouldRetry
};
function defaultShouldRetry(error) {
  if (error && typeof error.status === "number") {
    const status = error.status;
    if (status === 429 || status >= 500 && status < 600) {
      return true;
    }
  }
  if (error instanceof Error && error.message) {
    if (error.message.includes("429")) return true;
    if (error.message.match(/5\d{2}/)) return true;
  }
  return false;
}
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
async function retryWithBackoff(fn, options) {
  const {
    maxAttempts,
    initialDelayMs,
    maxDelayMs,
    onPersistent429,
    authType,
    shouldRetry
  } = {
    ...DEFAULT_RETRY_OPTIONS,
    ...options
  };
  let attempt = 0;
  let currentDelay = initialDelayMs;
  let consecutive429Count = 0;
  while (attempt < maxAttempts) {
    attempt++;
    try {
      return await fn();
    } catch (error) {
      const errorStatus = getErrorStatus(error);
      if (errorStatus === 429) {
        consecutive429Count++;
      } else {
        consecutive429Count = 0;
      }
      if (consecutive429Count >= 2 && onPersistent429 && authType === AuthType.LOGIN_WITH_GOOGLE) {
        try {
          const fallbackModel = await onPersistent429(authType);
          if (fallbackModel) {
            attempt = 0;
            consecutive429Count = 0;
            currentDelay = initialDelayMs;
            continue;
          }
        } catch (fallbackError) {
          console.warn("Fallback to Flash model failed:", fallbackError);
        }
      }
      if (attempt >= maxAttempts || !shouldRetry(error)) {
        throw error;
      }
      const { delayDurationMs, errorStatus: delayErrorStatus } = getDelayDurationAndStatus(error);
      if (delayDurationMs > 0) {
        console.warn(
          `Attempt ${attempt} failed with status ${delayErrorStatus ?? "unknown"}. Retrying after explicit delay of ${delayDurationMs}ms...`,
          error
        );
        await delay(delayDurationMs);
        currentDelay = initialDelayMs;
      } else {
        logRetryAttempt(attempt, error, errorStatus);
        const jitter = currentDelay * 0.3 * (Math.random() * 2 - 1);
        const delayWithJitter = Math.max(0, currentDelay + jitter);
        await delay(delayWithJitter);
        currentDelay = Math.min(maxDelayMs, currentDelay * 2);
      }
    }
  }
  throw new Error("Retry attempts exhausted");
}
function getErrorStatus(error) {
  if (typeof error === "object" && error !== null) {
    if ("status" in error && typeof error.status === "number") {
      return error.status;
    }
    if ("response" in error && typeof error.response === "object" && error.response !== null) {
      const response = error.response;
      if ("status" in response && typeof response.status === "number") {
        return response.status;
      }
    }
  }
  return void 0;
}
function getRetryAfterDelayMs(error) {
  if (typeof error === "object" && error !== null) {
    if ("response" in error && typeof error.response === "object" && error.response !== null) {
      const response = error.response;
      if ("headers" in response && typeof response.headers === "object" && response.headers !== null) {
        const headers = response.headers;
        const retryAfterHeader = headers["retry-after"];
        if (typeof retryAfterHeader === "string") {
          const retryAfterSeconds = parseInt(retryAfterHeader, 10);
          if (!isNaN(retryAfterSeconds)) {
            return retryAfterSeconds * 1e3;
          }
          const retryAfterDate = new Date(retryAfterHeader);
          if (!isNaN(retryAfterDate.getTime())) {
            return Math.max(0, retryAfterDate.getTime() - Date.now());
          }
        }
      }
    }
  }
  return 0;
}
function getDelayDurationAndStatus(error) {
  const errorStatus = getErrorStatus(error);
  let delayDurationMs = 0;
  if (errorStatus === 429) {
    delayDurationMs = getRetryAfterDelayMs(error);
  }
  return { delayDurationMs, errorStatus };
}
function logRetryAttempt(attempt, error, errorStatus) {
  let message = `Attempt ${attempt} failed. Retrying with backoff...`;
  if (errorStatus) {
    message = `Attempt ${attempt} failed with status ${errorStatus}. Retrying with backoff...`;
  }
  if (errorStatus === 429) {
    console.warn(message, error);
  } else if (errorStatus && errorStatus >= 500 && errorStatus < 600) {
    console.error(message, error);
  } else if (error instanceof Error) {
    if (error.message.includes("429")) {
      console.warn(
        `Attempt ${attempt} failed with 429 error (no Retry-After header). Retrying with backoff...`,
        error
      );
    } else if (error.message.match(/5\d{2}/)) {
      console.error(
        `Attempt ${attempt} failed with 5xx error. Retrying with backoff...`,
        error
      );
    } else {
      console.warn(message, error);
    }
  } else {
    console.warn(message, error);
  }
}
export {
  retryWithBackoff
};
//# sourceMappingURL=retry.js.map
