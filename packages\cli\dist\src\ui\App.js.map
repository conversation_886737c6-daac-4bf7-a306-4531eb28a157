{"version": 3, "sources": ["../../../src/ui/App.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { Box, Text, useInput, useApp } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\nimport { ChatInterface } from '../components/ChatInterface.js';\nimport { StatusBar } from '../components/StatusBar.js';\nimport { WelcomeScreen } from '../components/WelcomeScreen.js';\nimport { ErrorBoundary } from '../components/ErrorBoundary.js';\nimport { ServiceStatus } from '../components/ServiceStatus.js';\nimport { SettingsScreen } from '../components/SettingsScreen.js';\nimport { useConfig } from '../hooks/useConfig.js';\nimport { useChat } from '../hooks/useChat.js';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts.js';\n\ntype Screen = 'welcome' | 'chat' | 'settings' | 'help' | 'services';\n\ninterface AppState {\n  screen: Screen;\n  isLoading: boolean;\n  error?: string;\n}\n\ninterface AppProps {\n  config: Config;\n  settings?: Config; // Optional for backward compatibility\n  startupWarnings?: string[];\n  initialCommand?: string;\n  debug?: boolean;\n}\n\nexport const AppWrapper = (props: AppProps) => {\n  return <App {...props} />;\n};\n\nconst App = ({\n  config: initialConfig,\n  settings,\n  startupWarnings = [],\n  initialCommand,\n  debug = false\n}: AppProps) => {\n  // Suppress unused parameter warnings for backward compatibility\n  void settings;\n  void startupWarnings;\n\n  const { exit } = useApp();\n  const [state, setState] = useState<AppState>({\n    screen: initialCommand ? 'chat' : 'welcome',\n    isLoading: false,\n  });\n\n  // Use integrated hooks\n  const { config, updateConfig, isConfigValid, configErrors } = useConfig(initialConfig);\n  const {\n    messages,\n    isGenerating,\n    sendMessage,\n    clearChat,\n    error: chatError,\n  } = useChat(config);\n\n  // Keyboard shortcuts integration\n  useKeyboardShortcuts({\n    onExit: () => exit(),\n    onClear: clearChat,\n    onToggleHelp: () => setState(prev => ({\n      ...prev,\n      screen: prev.screen === 'help' ? 'chat' : 'help',\n    })),\n  });\n\n  // Handle initial command\n  useEffect(() => {\n    if (initialCommand && state.screen === 'chat' && isConfigValid) {\n      sendMessage(initialCommand);\n    }\n  }, [initialCommand, state.screen, isConfigValid, sendMessage]);\n\n  // Handle configuration validation\n  useEffect(() => {\n    if (!isConfigValid) {\n      setState(prev => ({\n        ...prev,\n        error: 'Configuration is invalid. Please check your settings.',\n      }));\n    } else {\n      setState(prev => ({ ...prev, error: undefined }));\n    }\n  }, [isConfigValid]);\n\n  // Handle chat errors\n  useEffect(() => {\n    if (chatError) {\n      setState(prev => ({ ...prev, error: chatError }));\n    }\n  }, [chatError]);\n\n  const handleScreenChange = useCallback((screen: Screen) => {\n    setState(prev => ({ ...prev, screen }));\n  }, []);\n\n  const handleStartChat = useCallback(() => {\n    if (isConfigValid) {\n      handleScreenChange('chat');\n    } else {\n      handleScreenChange('settings');\n    }\n  }, [isConfigValid, handleScreenChange]);\n\n  const renderScreen = () => {\n    switch (state.screen) {\n      case 'welcome':\n        return (\n          <WelcomeScreen\n            config={config}\n            onStartChat={handleStartChat}\n            onShowSettings={() => handleScreenChange('settings')}\n            onShowHelp={() => handleScreenChange('help')}\n          />\n        );\n\n      case 'chat':\n        return (\n          <ChatInterface\n            messages={messages}\n            isGenerating={isGenerating}\n            onSendMessage={sendMessage}\n            onClear={clearChat}\n            config={config}\n          />\n        );\n\n      case 'settings':\n        return (\n          <SettingsScreen\n            config={config}\n            configErrors={configErrors}\n            isConfigValid={isConfigValid}\n          />\n        );\n\n      case 'help':\n        return (\n          <Box flexDirection=\"column\">\n            <Text color=\"cyan\" bold>Arien CLI Help</Text>\n            <Text></Text>\n            <Text color=\"white\">Keyboard Shortcuts:</Text>\n            <Text color=\"gray\"> Ctrl+C / q - Exit</Text>\n            <Text color=\"gray\"> Ctrl+L - Clear chat</Text>\n            <Text color=\"gray\"> Ctrl+H / ? - Toggle help</Text>\n            <Text color=\"gray\"> Ctrl+S - Service status</Text>\n            <Text color=\"gray\"> Tab - Autocomplete</Text>\n            <Text color=\"gray\"> ↑/↓ - Command history</Text>\n            <Text></Text>\n            <Text color=\"white\">Commands:</Text>\n            <Text color=\"gray\"> /clear - Clear chat history</Text>\n            <Text color=\"gray\"> /help - Show this help</Text>\n            <Text color=\"gray\"> /quit - Exit application</Text>\n            <Text color=\"gray\"> /settings - Open settings</Text>\n            <Text color=\"gray\"> /services - Show service status</Text>\n            <Text></Text>\n            <Text color=\"gray\">Press any key to return to chat</Text>\n          </Box>\n        );\n\n      case 'services':\n        return <ServiceStatus config={config} />;\n\n      default:\n        return <Text color=\"red\">Unknown screen: {state.screen}</Text>;\n    }\n  };\n\n  // Handle input for different screens\n  useInput((input, key) => {\n    if (state.screen === 'help') {\n      handleScreenChange('chat');\n      return;\n    }\n\n    if (state.screen === 'services') {\n      handleScreenChange('chat');\n      return;\n    }\n\n    if (state.screen === 'settings') {\n      if (input === 'q') {\n        exit();\n      } else if (input === 'c') {\n        handleScreenChange('chat');\n      }\n      return;\n    }\n\n    if (state.screen === 'welcome') {\n      if (input === 'q') {\n        exit();\n      } else if (input === 'c' || key.return) {\n        handleStartChat();\n      } else if (input === 's') {\n        handleScreenChange('settings');\n      } else if (input === 'v') {\n        handleScreenChange('services');\n      } else if (input === 'h' || input === '?') {\n        handleScreenChange('help');\n      }\n      return;\n    }\n  });\n\n  return (\n    <ErrorBoundary>\n      <Box flexDirection=\"column\" height=\"100%\">\n        <StatusBar\n          config={config}\n          isGenerating={isGenerating}\n          error={state.error}\n          screen={state.screen}\n          showServiceStatus={true}\n        />\n\n        <Box flexGrow={1} flexDirection=\"column\">\n          {state.isLoading ? (\n            <Box justifyContent=\"center\" alignItems=\"center\" flexGrow={1}>\n              <Text color=\"yellow\">Loading...</Text>\n            </Box>\n          ) : (\n            renderScreen()\n          )}\n        </Box>\n\n        {debug && (\n          <Box borderStyle=\"single\" borderColor=\"gray\" padding={1}>\n            <Text color=\"gray\" dimColor>\n              Debug: Screen={state.screen} | Config Valid={isConfigValid} | Messages={messages.length} | Generating={isGenerating}\n            </Text>\n          </Box>\n        )}\n      </Box>\n    </ErrorBoundary>\n  );\n};\n\nexport default App;\n"], "mappings": "AAoCS,cAgHC,YAhHD;AApCT;AAAA;AAAA;AAAA;AAAA;AAMA,SAAS,UAAU,WAAW,mBAAmB;AACjD,SAAS,KAAK,MAAM,UAAU,cAAc;AAE5C,SAAS,qBAAqB;AAC9B,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,qBAAqB;AAC9B,SAAS,sBAAsB;AAC/B,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,4BAA4B;AAkB9B,MAAM,aAAa,CAAC,UAAoB;AAC7C,SAAO,oBAAC,OAAK,GAAG,OAAO;AACzB;AAEA,MAAM,MAAM,CAAC;AAAA,EACX,QAAQ;AAAA,EACR;AAAA,EACA,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,QAAQ;AACV,MAAgB;AAEd,OAAK;AACL,OAAK;AAEL,QAAM,EAAE,KAAK,IAAI,OAAO;AACxB,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAmB;AAAA,IAC3C,QAAQ,iBAAiB,SAAS;AAAA,IAClC,WAAW;AAAA,EACb,CAAC;AAGD,QAAM,EAAE,QAAQ,cAAc,eAAe,aAAa,IAAI,UAAU,aAAa;AACrF,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,IAAI,QAAQ,MAAM;AAGlB,uBAAqB;AAAA,IACnB,QAAQ,MAAM,KAAK;AAAA,IACnB,SAAS;AAAA,IACT,cAAc,MAAM,SAAS,WAAS;AAAA,MACpC,GAAG;AAAA,MACH,QAAQ,KAAK,WAAW,SAAS,SAAS;AAAA,IAC5C,EAAE;AAAA,EACJ,CAAC;AAGD,YAAU,MAAM;AACd,QAAI,kBAAkB,MAAM,WAAW,UAAU,eAAe;AAC9D,kBAAY,cAAc;AAAA,IAC5B;AAAA,EACF,GAAG,CAAC,gBAAgB,MAAM,QAAQ,eAAe,WAAW,CAAC;AAG7D,YAAU,MAAM;AACd,QAAI,CAAC,eAAe;AAClB,eAAS,WAAS;AAAA,QAChB,GAAG;AAAA,QACH,OAAO;AAAA,MACT,EAAE;AAAA,IACJ,OAAO;AACL,eAAS,WAAS,EAAE,GAAG,MAAM,OAAO,OAAU,EAAE;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAGlB,YAAU,MAAM;AACd,QAAI,WAAW;AACb,eAAS,WAAS,EAAE,GAAG,MAAM,OAAO,UAAU,EAAE;AAAA,IAClD;AAAA,EACF,GAAG,CAAC,SAAS,CAAC;AAEd,QAAM,qBAAqB,YAAY,CAAC,WAAmB;AACzD,aAAS,WAAS,EAAE,GAAG,MAAM,OAAO,EAAE;AAAA,EACxC,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAkB,YAAY,MAAM;AACxC,QAAI,eAAe;AACjB,yBAAmB,MAAM;AAAA,IAC3B,OAAO;AACL,yBAAmB,UAAU;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,eAAe,kBAAkB,CAAC;AAEtC,QAAM,eAAe,MAAM;AACzB,YAAQ,MAAM,QAAQ;AAAA,MACpB,KAAK;AACH,eACE;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA,aAAa;AAAA,YACb,gBAAgB,MAAM,mBAAmB,UAAU;AAAA,YACnD,YAAY,MAAM,mBAAmB,MAAM;AAAA;AAAA,QAC7C;AAAA,MAGJ,KAAK;AACH,eACE;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA;AAAA,YACA,eAAe;AAAA,YACf,SAAS;AAAA,YACT;AAAA;AAAA,QACF;AAAA,MAGJ,KAAK;AACH,eACE;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA;AAAA,QACF;AAAA,MAGJ,KAAK;AACH,eACE,qBAAC,OAAI,eAAc,UACjB;AAAA,8BAAC,QAAK,OAAM,QAAO,MAAI,MAAC,4BAAc;AAAA,UACtC,oBAAC,QAAK;AAAA,UACN,oBAAC,QAAK,OAAM,SAAQ,iCAAmB;AAAA,UACvC,oBAAC,QAAK,OAAM,QAAO,gCAAkB;AAAA,UACrC,oBAAC,QAAK,OAAM,QAAO,kCAAoB;AAAA,UACvC,oBAAC,QAAK,OAAM,QAAO,uCAAyB;AAAA,UAC5C,oBAAC,QAAK,OAAM,QAAO,sCAAwB;AAAA,UAC3C,oBAAC,QAAK,OAAM,QAAO,iCAAmB;AAAA,UACtC,oBAAC,QAAK,OAAM,QAAO,8CAAsB;AAAA,UACzC,oBAAC,QAAK;AAAA,UACN,oBAAC,QAAK,OAAM,SAAQ,uBAAS;AAAA,UAC7B,oBAAC,QAAK,OAAM,QAAO,0CAA4B;AAAA,UAC/C,oBAAC,QAAK,OAAM,QAAO,qCAAuB;AAAA,UAC1C,oBAAC,QAAK,OAAM,QAAO,uCAAyB;AAAA,UAC5C,oBAAC,QAAK,OAAM,QAAO,wCAA0B;AAAA,UAC7C,oBAAC,QAAK,OAAM,QAAO,8CAAgC;AAAA,UACnD,oBAAC,QAAK;AAAA,UACN,oBAAC,QAAK,OAAM,QAAO,6CAA+B;AAAA,WACpD;AAAA,MAGJ,KAAK;AACH,eAAO,oBAAC,iBAAc,QAAgB;AAAA,MAExC;AACE,eAAO,qBAAC,QAAK,OAAM,OAAM;AAAA;AAAA,UAAiB,MAAM;AAAA,WAAO;AAAA,IAC3D;AAAA,EACF;AAGA,WAAS,CAAC,OAAO,QAAQ;AACvB,QAAI,MAAM,WAAW,QAAQ;AAC3B,yBAAmB,MAAM;AACzB;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,YAAY;AAC/B,yBAAmB,MAAM;AACzB;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,YAAY;AAC/B,UAAI,UAAU,KAAK;AACjB,aAAK;AAAA,MACP,WAAW,UAAU,KAAK;AACxB,2BAAmB,MAAM;AAAA,MAC3B;AACA;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,WAAW;AAC9B,UAAI,UAAU,KAAK;AACjB,aAAK;AAAA,MACP,WAAW,UAAU,OAAO,IAAI,QAAQ;AACtC,wBAAgB;AAAA,MAClB,WAAW,UAAU,KAAK;AACxB,2BAAmB,UAAU;AAAA,MAC/B,WAAW,UAAU,KAAK;AACxB,2BAAmB,UAAU;AAAA,MAC/B,WAAW,UAAU,OAAO,UAAU,KAAK;AACzC,2BAAmB,MAAM;AAAA,MAC3B;AACA;AAAA,IACF;AAAA,EACF,CAAC;AAED,SACE,oBAAC,iBACC,+BAAC,OAAI,eAAc,UAAS,QAAO,QACjC;AAAA;AAAA,MAAC;AAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA,OAAO,MAAM;AAAA,QACb,QAAQ,MAAM;AAAA,QACd,mBAAmB;AAAA;AAAA,IACrB;AAAA,IAEA,oBAAC,OAAI,UAAU,GAAG,eAAc,UAC7B,gBAAM,YACL,oBAAC,OAAI,gBAAe,UAAS,YAAW,UAAS,UAAU,GACzD,8BAAC,QAAK,OAAM,UAAS,wBAAU,GACjC,IAEA,aAAa,GAEjB;AAAA,IAEC,SACC,oBAAC,OAAI,aAAY,UAAS,aAAY,QAAO,SAAS,GACpD,+BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC;AAAA;AAAA,MACX,MAAM;AAAA,MAAO;AAAA,MAAiB;AAAA,MAAc;AAAA,MAAa,SAAS;AAAA,MAAO;AAAA,MAAe;AAAA,OACzG,GACF;AAAA,KAEJ,GACF;AAEJ;AAEA,IAAO,cAAQ;", "names": []}