{"version": 3, "sources": ["../../../src/services/mcp-server-manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport { Config, MCPServerConfig } from '../config/config.js';\nimport { logger } from '../core/logger.js';\nimport { ToolRegistry } from '../tools/tool-registry.js';\nimport { MCPClientTool } from '../tools/mcp-client.js';\nimport { MCPToolFactory } from '../tools/mcp-tool.js';\nimport { Client } from '@modelcontextprotocol/sdk/client/index.js';\nimport { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';\nimport { getErrorMessage } from '../utils/errors.js';\n\ninterface ManagedMCPServer {\n  name: string;\n  config: MCPServerConfig;\n  client?: Client;\n  transport?: StdioClientTransport;\n  connected: boolean;\n  tools: string[];\n  lastError?: string;\n  retryCount: number;\n}\n\n/**\n * Service for managing MCP server lifecycle and automatic startup\n */\nexport class MCPServerManager {\n  private config: Config;\n  private toolRegistry: ToolRegistry;\n  private servers: Map<string, ManagedMCPServer> = new Map();\n  private mcpToolFactory: MCPToolFactory;\n  private initialized: boolean = false;\n\n  constructor(config: Config, toolRegistry: ToolRegistry) {\n    this.config = config;\n    this.toolRegistry = toolRegistry;\n    this.mcpToolFactory = new MCPToolFactory(this.createToolCallHandler());\n  }\n\n  /**\n   * Initialize MCP servers based on configuration\n   */\n  async initialize(): Promise<void> {\n    if (this.initialized) {\n      return;\n    }\n\n    logger.debug('Initializing MCP Server Manager');\n\n    const mcpServers = this.config.getMcpServers();\n    const serverNames = Object.keys(mcpServers);\n\n    if (serverNames.length === 0) {\n      logger.debug('No MCP servers configured');\n      this.initialized = true;\n      return;\n    }\n\n    logger.info(`Starting ${serverNames.length} MCP servers: ${serverNames.join(', ')}`);\n\n    // Start all configured servers\n    const startupPromises = serverNames.map(async (serverName) => {\n      const serverConfig = mcpServers[serverName];\n      try {\n        await this.startServer(serverName, serverConfig);\n        logger.info(`Successfully started MCP server: ${serverName}`);\n      } catch (error) {\n        logger.error(`Failed to start MCP server ${serverName}:`, error);\n      }\n    });\n\n    // Wait for all servers to attempt startup\n    await Promise.allSettled(startupPromises);\n\n    this.initialized = true;\n    logger.debug('MCP Server Manager initialization complete');\n  }\n\n  /**\n   * Start a single MCP server\n   */\n  private async startServer(name: string, config: MCPServerConfig): Promise<void> {\n    const server: ManagedMCPServer = {\n      name,\n      config,\n      connected: false,\n      tools: [],\n      retryCount: 0,\n    };\n\n    this.servers.set(name, server);\n\n    try {\n      // Create transport and client\n      const transport = new StdioClientTransport({\n        command: config.command,\n        args: config.args || [],\n        env: this.createServerEnvironment(config.env || {}),\n      });\n\n      const client = new Client(\n        {\n          name: 'arien-cli',\n          version: '0.1.0',\n        },\n        {\n          capabilities: {\n            tools: {},\n            resources: {},\n            prompts: {},\n          },\n        },\n      );\n\n      // Connect to the server\n      await client.connect(transport);\n\n      // List available tools\n      const toolsResponse = await client.listTools();\n      const toolNames = toolsResponse.tools.map(tool => tool.name);\n\n      // Update server state\n      server.client = client;\n      server.transport = transport;\n      server.connected = true;\n      server.tools = toolNames;\n\n      // Register MCP tools with the tool registry\n      await this.registerServerTools(name, toolsResponse.tools);\n\n      logger.debug(`MCP server ${name} connected with ${toolNames.length} tools: ${toolNames.join(', ')}`);\n    } catch (error) {\n      server.lastError = getErrorMessage(error);\n      server.connected = false;\n      throw error;\n    }\n  }\n\n  /**\n   * Register tools from an MCP server with the tool registry\n   */\n  private async registerServerTools(serverName: string, toolDefinitions: any[]): Promise<void> {\n    for (const toolDef of toolDefinitions) {\n      try {\n        const mcpTool = this.mcpToolFactory.createTool(serverName, toolDef.name, toolDef);\n        this.toolRegistry.registerMCPTool(mcpTool);\n        logger.debug(`Registered MCP tool: ${mcpTool.name} from server: ${serverName}`);\n      } catch (error) {\n        logger.error(`Failed to register MCP tool ${toolDef.name} from server ${serverName}:`, error);\n      }\n    }\n  }\n\n  /**\n   * Create environment for MCP server process\n   */\n  private createServerEnvironment(serverEnv: Record<string, string>): Record<string, string> {\n    const cleanEnv: Record<string, string> = {};\n    \n    // Start with process environment, filtering out undefined values\n    for (const [key, value] of Object.entries(process.env)) {\n      if (value !== undefined) {\n        cleanEnv[key] = value;\n      }\n    }\n\n    // Add server-specific environment variables\n    for (const [key, value] of Object.entries(serverEnv)) {\n      cleanEnv[key] = value;\n    }\n\n    return cleanEnv;\n  }\n\n  /**\n   * Create tool call handler for MCP tools\n   */\n  private createToolCallHandler() {\n    return async (serverName: string, toolName: string, params: any): Promise<any> => {\n      const server = this.servers.get(serverName);\n      if (!server) {\n        throw new Error(`MCP server not found: ${serverName}`);\n      }\n\n      if (!server.connected || !server.client) {\n        throw new Error(`MCP server not connected: ${serverName}`);\n      }\n\n      try {\n        const result = await server.client.callTool({\n          name: toolName,\n          arguments: params || {},\n        });\n\n        return result.content;\n      } catch (error) {\n        throw new Error(`MCP tool call failed: ${getErrorMessage(error)}`);\n      }\n    };\n  }\n\n  /**\n   * Stop a specific MCP server\n   */\n  async stopServer(serverName: string): Promise<void> {\n    const server = this.servers.get(serverName);\n    if (!server) {\n      return;\n    }\n\n    try {\n      // Unregister tools from the tool registry\n      this.toolRegistry.unregisterMCPServerTools(serverName);\n\n      // Close transport connection\n      if (server.transport) {\n        await server.transport.close();\n      }\n\n      server.connected = false;\n      server.client = undefined;\n      server.transport = undefined;\n\n      logger.debug(`Stopped MCP server: ${serverName}`);\n    } catch (error) {\n      logger.error(`Error stopping MCP server ${serverName}:`, error);\n      server.lastError = getErrorMessage(error);\n    }\n  }\n\n  /**\n   * Stop all MCP servers\n   */\n  async stopAllServers(): Promise<void> {\n    const serverNames = Array.from(this.servers.keys());\n    const stopPromises = serverNames.map(name => this.stopServer(name));\n    await Promise.allSettled(stopPromises);\n    logger.debug('Stopped all MCP servers');\n  }\n\n  /**\n   * Get status of all managed servers\n   */\n  getServerStatus(): Record<string, { connected: boolean; tools: string[]; lastError?: string }> {\n    const status: Record<string, { connected: boolean; tools: string[]; lastError?: string }> = {};\n    \n    for (const [name, server] of this.servers) {\n      status[name] = {\n        connected: server.connected,\n        tools: server.tools,\n        lastError: server.lastError,\n      };\n    }\n\n    return status;\n  }\n\n  /**\n   * Check if the manager is initialized\n   */\n  isInitialized(): boolean {\n    return this.initialized;\n  }\n\n  /**\n   * Get connected server count\n   */\n  getConnectedServerCount(): number {\n    return Array.from(this.servers.values()).filter(server => server.connected).length;\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,cAAc;AAGvB,SAAS,sBAAsB;AAC/B,SAAS,cAAc;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAgBzB,MAAM,iBAAiB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,UAAyC,oBAAI,IAAI;AAAA,EACjD;AAAA,EACA,cAAuB;AAAA,EAE/B,YAAY,QAAgB,cAA4B;AACtD,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,iBAAiB,IAAI,eAAe,KAAK,sBAAsB,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAA4B;AAChC,QAAI,KAAK,aAAa;AACpB;AAAA,IACF;AAEA,WAAO,MAAM,iCAAiC;AAE9C,UAAM,aAAa,KAAK,OAAO,cAAc;AAC7C,UAAM,cAAc,OAAO,KAAK,UAAU;AAE1C,QAAI,YAAY,WAAW,GAAG;AAC5B,aAAO,MAAM,2BAA2B;AACxC,WAAK,cAAc;AACnB;AAAA,IACF;AAEA,WAAO,KAAK,YAAY,YAAY,MAAM,iBAAiB,YAAY,KAAK,IAAI,CAAC,EAAE;AAGnF,UAAM,kBAAkB,YAAY,IAAI,OAAO,eAAe;AAC5D,YAAM,eAAe,WAAW,UAAU;AAC1C,UAAI;AACF,cAAM,KAAK,YAAY,YAAY,YAAY;AAC/C,eAAO,KAAK,oCAAoC,UAAU,EAAE;AAAA,MAC9D,SAAS,OAAO;AACd,eAAO,MAAM,8BAA8B,UAAU,KAAK,KAAK;AAAA,MACjE;AAAA,IACF,CAAC;AAGD,UAAM,QAAQ,WAAW,eAAe;AAExC,SAAK,cAAc;AACnB,WAAO,MAAM,4CAA4C;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,YAAY,MAAc,QAAwC;AAC9E,UAAM,SAA2B;AAAA,MAC/B;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX,OAAO,CAAC;AAAA,MACR,YAAY;AAAA,IACd;AAEA,SAAK,QAAQ,IAAI,MAAM,MAAM;AAE7B,QAAI;AAEF,YAAM,YAAY,IAAI,qBAAqB;AAAA,QACzC,SAAS,OAAO;AAAA,QAChB,MAAM,OAAO,QAAQ,CAAC;AAAA,QACtB,KAAK,KAAK,wBAAwB,OAAO,OAAO,CAAC,CAAC;AAAA,MACpD,CAAC;AAED,YAAM,SAAS,IAAI;AAAA,QACjB;AAAA,UACE,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,QACA;AAAA,UACE,cAAc;AAAA,YACZ,OAAO,CAAC;AAAA,YACR,WAAW,CAAC;AAAA,YACZ,SAAS,CAAC;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAGA,YAAM,OAAO,QAAQ,SAAS;AAG9B,YAAM,gBAAgB,MAAM,OAAO,UAAU;AAC7C,YAAM,YAAY,cAAc,MAAM,IAAI,UAAQ,KAAK,IAAI;AAG3D,aAAO,SAAS;AAChB,aAAO,YAAY;AACnB,aAAO,YAAY;AACnB,aAAO,QAAQ;AAGf,YAAM,KAAK,oBAAoB,MAAM,cAAc,KAAK;AAExD,aAAO,MAAM,cAAc,IAAI,mBAAmB,UAAU,MAAM,WAAW,UAAU,KAAK,IAAI,CAAC,EAAE;AAAA,IACrG,SAAS,OAAO;AACd,aAAO,YAAY,gBAAgB,KAAK;AACxC,aAAO,YAAY;AACnB,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,oBAAoB,YAAoB,iBAAuC;AAC3F,eAAW,WAAW,iBAAiB;AACrC,UAAI;AACF,cAAM,UAAU,KAAK,eAAe,WAAW,YAAY,QAAQ,MAAM,OAAO;AAChF,aAAK,aAAa,gBAAgB,OAAO;AACzC,eAAO,MAAM,wBAAwB,QAAQ,IAAI,iBAAiB,UAAU,EAAE;AAAA,MAChF,SAAS,OAAO;AACd,eAAO,MAAM,+BAA+B,QAAQ,IAAI,gBAAgB,UAAU,KAAK,KAAK;AAAA,MAC9F;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,wBAAwB,WAA2D;AACzF,UAAM,WAAmC,CAAC;AAG1C,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,GAAG,GAAG;AACtD,UAAI,UAAU,QAAW;AACvB,iBAAS,GAAG,IAAI;AAAA,MAClB;AAAA,IACF;AAGA,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,SAAS,GAAG;AACpD,eAAS,GAAG,IAAI;AAAA,IAClB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,wBAAwB;AAC9B,WAAO,OAAO,YAAoB,UAAkB,WAA8B;AAChF,YAAM,SAAS,KAAK,QAAQ,IAAI,UAAU;AAC1C,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,yBAAyB,UAAU,EAAE;AAAA,MACvD;AAEA,UAAI,CAAC,OAAO,aAAa,CAAC,OAAO,QAAQ;AACvC,cAAM,IAAI,MAAM,6BAA6B,UAAU,EAAE;AAAA,MAC3D;AAEA,UAAI;AACF,cAAM,SAAS,MAAM,OAAO,OAAO,SAAS;AAAA,UAC1C,MAAM;AAAA,UACN,WAAW,UAAU,CAAC;AAAA,QACxB,CAAC;AAED,eAAO,OAAO;AAAA,MAChB,SAAS,OAAO;AACd,cAAM,IAAI,MAAM,yBAAyB,gBAAgB,KAAK,CAAC,EAAE;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,YAAmC;AAClD,UAAM,SAAS,KAAK,QAAQ,IAAI,UAAU;AAC1C,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AAEA,QAAI;AAEF,WAAK,aAAa,yBAAyB,UAAU;AAGrD,UAAI,OAAO,WAAW;AACpB,cAAM,OAAO,UAAU,MAAM;AAAA,MAC/B;AAEA,aAAO,YAAY;AACnB,aAAO,SAAS;AAChB,aAAO,YAAY;AAEnB,aAAO,MAAM,uBAAuB,UAAU,EAAE;AAAA,IAClD,SAAS,OAAO;AACd,aAAO,MAAM,6BAA6B,UAAU,KAAK,KAAK;AAC9D,aAAO,YAAY,gBAAgB,KAAK;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAgC;AACpC,UAAM,cAAc,MAAM,KAAK,KAAK,QAAQ,KAAK,CAAC;AAClD,UAAM,eAAe,YAAY,IAAI,UAAQ,KAAK,WAAW,IAAI,CAAC;AAClE,UAAM,QAAQ,WAAW,YAAY;AACrC,WAAO,MAAM,yBAAyB;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAA+F;AAC7F,UAAM,SAAsF,CAAC;AAE7F,eAAW,CAAC,MAAM,MAAM,KAAK,KAAK,SAAS;AACzC,aAAO,IAAI,IAAI;AAAA,QACb,WAAW,OAAO;AAAA,QAClB,OAAO,OAAO;AAAA,QACd,WAAW,OAAO;AAAA,MACpB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,0BAAkC;AAChC,WAAO,MAAM,KAAK,KAAK,QAAQ,OAAO,CAAC,EAAE,OAAO,YAAU,OAAO,SAAS,EAAE;AAAA,EAC9E;AACF;", "names": []}