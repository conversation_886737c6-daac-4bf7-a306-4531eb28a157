/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import { Config } from '@arien/arien-cli-core';

interface StatusBarProps {
  config: Config;
  isGenerating: boolean;
  error?: string;
  screen: string;
  showServiceStatus?: boolean;
}

interface ServiceStatus {
  fileService: boolean;
  gitService: boolean;
  mcpServers: number;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  config,
  isGenerating,
  error,
  screen,
  showServiceStatus = false,
}) => {
  const [serviceStatus, setServiceStatus] = useState<ServiceStatus>({
    fileService: false,
    gitService: false,
    mcpServers: 0,
  });

  // Check service status
  useEffect(() => {
    const checkServices = async () => {
      try {
        // Check FileDiscoveryService
        const fileService = config.getFileService();
        const fileServiceStatus = !!fileService;

        // Check GitService
        let gitServiceStatus = false;
        try {
          const gitService = config.getGitService();
          const gitStatus = await gitService.getStatus();
          gitServiceStatus = gitStatus.isRepository;
        } catch {
          gitServiceStatus = false;
        }

        // Check MCP Servers
        let mcpServerCount = 0;
        try {
          const mcpManager = config.getMCPServerManager();
          const servers = mcpManager.getActiveServers();
          mcpServerCount = servers.length;
        } catch {
          mcpServerCount = 0;
        }

        setServiceStatus({
          fileService: fileServiceStatus,
          gitService: gitServiceStatus,
          mcpServers: mcpServerCount,
        });
      } catch (error) {
        // Silently handle service check errors
        console.debug('Service status check failed:', error);
      }
    };

    if (showServiceStatus) {
      checkServices();
    }
  }, [config, showServiceStatus]);
  const getStatusColor = () => {
    if (error) return 'red';
    if (isGenerating) return 'yellow';
    return 'green';
  };

  const getStatusText = () => {
    if (error) return '❌ Error';
    if (isGenerating) return '⏳ Generating...';
    return '✅ Ready';
  };

  const formatModel = (model: string) => {
    // Shorten common model names for display
    return model
      .replace('gemini-2.0-flash-exp', 'Gemini 2.0 Flash')
      .replace('gemini-1.5-pro', 'Gemini 1.5 Pro')
      .replace('gemini-1.5-flash', 'Gemini 1.5 Flash');
  };

  return (
    <Box
      borderStyle="single"
      borderColor="gray"
      paddingX={1}
      justifyContent="space-between"
    >
      {/* Left side - Status and Model */}
      <Box>
        <Text color={getStatusColor()} bold>
          {getStatusText()}
        </Text>
        <Text color="gray" dimColor>
          {' | '}
        </Text>
        <Text color="cyan">{formatModel(config.getModel())}</Text>
        {screen !== 'chat' && (
          <>
            <Text color="gray" dimColor>
              {' | '}
            </Text>
            <Text color="magenta">
              {screen.charAt(0).toUpperCase() + screen.slice(1)}
            </Text>
          </>
        )}
      </Box>

      {/* Right side - Service Status and Shortcuts */}
      <Box>
        {showServiceStatus && (
          <>
            <Text color={serviceStatus.fileService ? 'green' : 'red'}>
              📁
            </Text>
            <Text color={serviceStatus.gitService ? 'green' : 'yellow'}>
              {serviceStatus.gitService ? '🔗' : '📝'}
            </Text>
            {serviceStatus.mcpServers > 0 && (
              <Text color="blue">
                🔌{serviceStatus.mcpServers}
              </Text>
            )}
            <Text color="gray" dimColor>
              {' | '}
            </Text>
          </>
        )}

        {error ? (
          <Text color="red" dimColor>
            {error.length > 50 ? `${error.substring(0, 50)}...` : error}
          </Text>
        ) : (
          <Text color="gray" dimColor>
            Ctrl+C: Exit | Ctrl+L: Clear | Ctrl+H: Help
          </Text>
        )}
      </Box>
    </Box>
  );
};
