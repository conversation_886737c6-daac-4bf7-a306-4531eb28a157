{"version": 3, "sources": ["../../../src/components/StatusBar.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { Box, Text } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\n\ninterface StatusBarProps {\n  config: Config;\n  isGenerating: boolean;\n  error?: string;\n  screen: string;\n  showServiceStatus?: boolean;\n}\n\ninterface ServiceStatus {\n  fileService: boolean;\n  gitService: boolean;\n  mcpServers: number;\n}\n\nexport const StatusBar: React.FC<StatusBarProps> = ({\n  config,\n  isGenerating,\n  error,\n  screen,\n  showServiceStatus = false,\n}) => {\n  const [serviceStatus, setServiceStatus] = useState<ServiceStatus>({\n    fileService: false,\n    gitService: false,\n    mcpServers: 0,\n  });\n\n  // Check service status\n  useEffect(() => {\n    const checkServices = async () => {\n      try {\n        // Check FileDiscoveryService\n        const fileService = config.getFileService();\n        const fileServiceStatus = !!fileService;\n\n        // Check GitService\n        let gitServiceStatus = false;\n        try {\n          const gitService = config.getGitService();\n          const gitStatus = await gitService.getStatus();\n          gitServiceStatus = gitStatus.isRepository;\n        } catch {\n          gitServiceStatus = false;\n        }\n\n        // Check MCP Servers\n        let mcpServerCount = 0;\n        try {\n          const mcpManager = config.getMCPServerManager();\n          const servers = mcpManager.getActiveServers();\n          mcpServerCount = servers.length;\n        } catch {\n          mcpServerCount = 0;\n        }\n\n        setServiceStatus({\n          fileService: fileServiceStatus,\n          gitService: gitServiceStatus,\n          mcpServers: mcpServerCount,\n        });\n      } catch (error) {\n        // Silently handle service check errors\n        console.debug('Service status check failed:', error);\n      }\n    };\n\n    if (showServiceStatus) {\n      checkServices();\n    }\n  }, [config, showServiceStatus]);\n  const getStatusColor = () => {\n    if (error) return 'red';\n    if (isGenerating) return 'yellow';\n    return 'green';\n  };\n\n  const getStatusText = () => {\n    if (error) return '❌ Error';\n    if (isGenerating) return '⏳ Generating...';\n    return '✅ Ready';\n  };\n\n  const formatModel = (model: string) => {\n    // Shorten common model names for display\n    return model\n      .replace('gemini-2.0-flash-exp', 'Gemini 2.0 Flash')\n      .replace('gemini-1.5-pro', 'Gemini 1.5 Pro')\n      .replace('gemini-1.5-flash', 'Gemini 1.5 Flash');\n  };\n\n  return (\n    <Box\n      borderStyle=\"single\"\n      borderColor=\"gray\"\n      paddingX={1}\n      justifyContent=\"space-between\"\n    >\n      {/* Left side - Status and Model */}\n      <Box>\n        <Text color={getStatusColor()} bold>\n          {getStatusText()}\n        </Text>\n        <Text color=\"gray\" dimColor>\n          {' | '}\n        </Text>\n        <Text color=\"cyan\">{formatModel(config.getModel())}</Text>\n        {screen !== 'chat' && (\n          <>\n            <Text color=\"gray\" dimColor>\n              {' | '}\n            </Text>\n            <Text color=\"magenta\">\n              {screen.charAt(0).toUpperCase() + screen.slice(1)}\n            </Text>\n          </>\n        )}\n      </Box>\n\n      {/* Right side - Service Status and Shortcuts */}\n      <Box>\n        {showServiceStatus && (\n          <>\n            <Text color={serviceStatus.fileService ? 'green' : 'red'}>\n              📁\n            </Text>\n            <Text color={serviceStatus.gitService ? 'green' : 'yellow'}>\n              {serviceStatus.gitService ? '🔗' : '📝'}\n            </Text>\n            {serviceStatus.mcpServers > 0 && (\n              <Text color=\"blue\">\n                🔌{serviceStatus.mcpServers}\n              </Text>\n            )}\n            <Text color=\"gray\" dimColor>\n              {' | '}\n            </Text>\n          </>\n        )}\n\n        {error ? (\n          <Text color=\"red\" dimColor>\n            {error.length > 50 ? `${error.substring(0, 50)}...` : error}\n          </Text>\n        ) : (\n          <Text color=\"gray\" dimColor>\n            Ctrl+C: Exit | Ctrl+L: Clear | Ctrl+H: Help\n          </Text>\n        )}\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AA6GQ,SAQE,UARF,KAQE,YARF;AA7GR;AAAA;AAAA;AAAA;AAAA;AAMA,SAAgB,UAAU,iBAAiB;AAC3C,SAAS,KAAK,YAAY;AAiBnB,MAAM,YAAsC,CAAC;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,oBAAoB;AACtB,MAAM;AACJ,QAAM,CAAC,eAAe,gBAAgB,IAAI,SAAwB;AAAA,IAChE,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd,CAAC;AAGD,YAAU,MAAM;AACd,UAAM,gBAAgB,YAAY;AAChC,UAAI;AAEF,cAAM,cAAc,OAAO,eAAe;AAC1C,cAAM,oBAAoB,CAAC,CAAC;AAG5B,YAAI,mBAAmB;AACvB,YAAI;AACF,gBAAM,aAAa,OAAO,cAAc;AACxC,gBAAM,YAAY,MAAM,WAAW,UAAU;AAC7C,6BAAmB,UAAU;AAAA,QAC/B,QAAQ;AACN,6BAAmB;AAAA,QACrB;AAGA,YAAI,iBAAiB;AACrB,YAAI;AACF,gBAAM,aAAa,OAAO,oBAAoB;AAC9C,gBAAM,UAAU,WAAW,iBAAiB;AAC5C,2BAAiB,QAAQ;AAAA,QAC3B,QAAQ;AACN,2BAAiB;AAAA,QACnB;AAEA,yBAAiB;AAAA,UACf,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH,SAASA,QAAO;AAEd,gBAAQ,MAAM,gCAAgCA,MAAK;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,mBAAmB;AACrB,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,QAAQ,iBAAiB,CAAC;AAC9B,QAAM,iBAAiB,MAAM;AAC3B,QAAI,MAAO,QAAO;AAClB,QAAI,aAAc,QAAO;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,gBAAgB,MAAM;AAC1B,QAAI,MAAO,QAAO;AAClB,QAAI,aAAc,QAAO;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,CAAC,UAAkB;AAErC,WAAO,MACJ,QAAQ,wBAAwB,kBAAkB,EAClD,QAAQ,kBAAkB,gBAAgB,EAC1C,QAAQ,oBAAoB,kBAAkB;AAAA,EACnD;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,aAAY;AAAA,MACZ,aAAY;AAAA,MACZ,UAAU;AAAA,MACV,gBAAe;AAAA,MAGf;AAAA,6BAAC,OACC;AAAA,8BAAC,QAAK,OAAO,eAAe,GAAG,MAAI,MAChC,wBAAc,GACjB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB,iBACH;AAAA,UACA,oBAAC,QAAK,OAAM,QAAQ,sBAAY,OAAO,SAAS,CAAC,GAAE;AAAA,UAClD,WAAW,UACV,iCACE;AAAA,gCAAC,QAAK,OAAM,QAAO,UAAQ,MACxB,iBACH;AAAA,YACA,oBAAC,QAAK,OAAM,WACT,iBAAO,OAAO,CAAC,EAAE,YAAY,IAAI,OAAO,MAAM,CAAC,GAClD;AAAA,aACF;AAAA,WAEJ;AAAA,QAGA,qBAAC,OACE;AAAA,+BACC,iCACE;AAAA,gCAAC,QAAK,OAAO,cAAc,cAAc,UAAU,OAAO,uBAE1D;AAAA,YACA,oBAAC,QAAK,OAAO,cAAc,aAAa,UAAU,UAC/C,wBAAc,aAAa,cAAO,aACrC;AAAA,YACC,cAAc,aAAa,KAC1B,qBAAC,QAAK,OAAM,QAAO;AAAA;AAAA,cACd,cAAc;AAAA,eACnB;AAAA,YAEF,oBAAC,QAAK,OAAM,QAAO,UAAQ,MACxB,iBACH;AAAA,aACF;AAAA,UAGD,QACC,oBAAC,QAAK,OAAM,OAAM,UAAQ,MACvB,gBAAM,SAAS,KAAK,GAAG,MAAM,UAAU,GAAG,EAAE,CAAC,QAAQ,OACxD,IAEA,oBAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,yDAE5B;AAAA,WAEJ;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": ["error"]}