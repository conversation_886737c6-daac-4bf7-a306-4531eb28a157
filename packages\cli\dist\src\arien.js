import { jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from "react";
import { render } from "ink";
import { AppWrapper } from "./ui/App.js";
import { loadCliConfig } from "./config/config.js";
import { readStdin } from "./utils/readStdin.js";
import { basename } from "node:path";
import v8 from "node:v8";
import os from "node:os";
import { spawn } from "node:child_process";
import { start_sandbox } from "./utils/sandbox.js";
import {
  USER_SETTINGS_PATH
} from "./config/settings.js";
import { themeManager } from "./ui/themes/theme-manager.js";
import { getStartupWarnings } from "./utils/startupWarnings.js";
import { runNonInteractive } from "./nonInteractiveCli.js";
import { loadExtensions } from "./config/extension.js";
import { cleanupCheckpoints } from "./utils/cleanup.js";
import {
  ApprovalMode,
  EditTool,
  ShellTool,
  WriteFileTool,
  sessionId,
  logUserPrompt,
  AuthType
} from "@arien/arien-cli-core";
import { validateAuthMethod } from "./config/auth.js";
import { setMaxSizedBoxDebugging } from "./ui/components/shared/MaxSizedBox.js";
function getNodeMemoryArgs(config) {
  const totalMemoryMB = os.totalmem() / (1024 * 1024);
  const heapStats = v8.getHeapStatistics();
  const currentMaxOldSpaceSizeMb = Math.floor(
    heapStats.heap_size_limit / 1024 / 1024
  );
  const targetMaxOldSpaceSizeInMB = Math.floor(totalMemoryMB * 0.5);
  if (config.getDebugMode()) {
    console.debug(
      `Current heap size ${currentMaxOldSpaceSizeMb.toFixed(2)} MB`
    );
  }
  if (process.env.ARIEN_CLI_NO_RELAUNCH) {
    return [];
  }
  if (targetMaxOldSpaceSizeInMB > currentMaxOldSpaceSizeMb) {
    if (config.getDebugMode()) {
      console.debug(
        `Need to relaunch with more memory: ${targetMaxOldSpaceSizeInMB.toFixed(2)} MB`
      );
    }
    return [`--max-old-space-size=${targetMaxOldSpaceSizeInMB}`];
  }
  return [];
}
async function relaunchWithAdditionalArgs(additionalArgs) {
  const nodeArgs = [...additionalArgs, ...process.argv.slice(1)];
  const newEnv = { ...process.env, ARIEN_CLI_NO_RELAUNCH: "true" };
  const child = spawn(process.execPath, nodeArgs, {
    stdio: "inherit",
    env: newEnv
  });
  await new Promise((resolve) => child.on("close", resolve));
  process.exit(0);
}
async function main() {
  const workspaceRoot = process.cwd();
  const extensions = loadExtensions(workspaceRoot);
  const config = await loadCliConfig({}, extensions, sessionId);
  await cleanupCheckpoints();
  const settingsErrors = config.getSettingsErrors();
  if (settingsErrors.length > 0) {
    for (const error of settingsErrors) {
      let errorMessage = `Error in ${error.path}: ${error.message}`;
      if (!process.env.NO_COLOR) {
        errorMessage = `\x1B[31m${errorMessage}\x1B[0m`;
      }
      console.error(errorMessage);
      console.error(`Please fix ${error.path} and try again.`);
    }
    process.exit(1);
  }
  if (!config.getSelectedAuthType() && process.env.GEMINI_API_KEY) {
    config.setSelectedAuthType(AuthType.USE_GEMINI);
  }
  setMaxSizedBoxDebugging(config.getDebugMode());
  config.getFileService();
  if (config.getCheckpointingEnabled()) {
    try {
      await config.getGitService();
    } catch {
    }
  }
  try {
    await config.initializeMCPServers();
  } catch (error) {
    console.warn("Warning: Failed to initialize MCP servers:", error);
  }
  const theme = config.getTheme();
  if (theme) {
    if (!themeManager.setActiveTheme(theme)) {
      console.warn(`Warning: Theme "${theme}" not found.`);
    }
  }
  const memoryArgs = config.getAutoConfigureMaxOldSpaceSize() ? getNodeMemoryArgs(config) : [];
  if (!process.env.SANDBOX) {
    const sandboxConfig = config.getSandbox();
    if (sandboxConfig) {
      const selectedAuthType = config.getSelectedAuthType();
      if (selectedAuthType) {
        try {
          const err = validateAuthMethod(selectedAuthType);
          if (err) {
            throw new Error(err);
          }
          await config.refreshAuth(selectedAuthType);
        } catch (err) {
          console.error("Error authenticating:", err);
          process.exit(1);
        }
      }
      await start_sandbox(sandboxConfig, memoryArgs);
      process.exit(0);
    } else {
      if (memoryArgs.length > 0) {
        await relaunchWithAdditionalArgs(memoryArgs);
        process.exit(0);
      }
    }
  }
  let input = config.getQuestion();
  const startupWarnings = await getStartupWarnings();
  if (process.stdin.isTTY && input?.length === 0) {
    setWindowTitle(basename(workspaceRoot), config);
    render(
      /* @__PURE__ */ jsx(React.StrictMode, { children: /* @__PURE__ */ jsx(
        AppWrapper,
        {
          config,
          settings: config,
          startupWarnings
        }
      ) }),
      { exitOnCtrlC: false }
    );
    return;
  }
  if (!process.stdin.isTTY) {
    input += await readStdin();
  }
  if (!input) {
    console.error("No input provided via stdin.");
    process.exit(1);
  }
  logUserPrompt(config, {
    "event.name": "user_prompt",
    "event.timestamp": (/* @__PURE__ */ new Date()).toISOString(),
    prompt: input,
    prompt_length: input.length
  });
  const nonInteractiveConfig = await loadNonInteractiveConfig(
    config,
    extensions
  );
  await runNonInteractive(nonInteractiveConfig, input);
  process.exit(0);
}
function setWindowTitle(title, config) {
  if (!config.getHideWindowTitle()) {
    process.stdout.write(`\x1B]2; Arien - ${title} \x07`);
    process.on("exit", () => {
      process.stdout.write(`\x1B]2;\x07`);
    });
  }
}
process.on("unhandledRejection", (reason, _promise) => {
  console.error("=========================================");
  console.error("CRITICAL: Unhandled Promise Rejection!");
  console.error("=========================================");
  console.error("Reason:", reason);
  console.error("Stack trace may follow:");
  if (!(reason instanceof Error)) {
    console.error(reason);
  }
  process.exit(1);
});
async function loadNonInteractiveConfig(config, extensions) {
  let finalConfig = config;
  if (config.getApprovalMode() !== ApprovalMode.YOLO) {
    const existingExcludeTools = config.getExcludeTools() || [];
    const interactiveTools = [
      ShellTool.Name,
      EditTool.Name,
      WriteFileTool.Name
    ];
    const newExcludeTools = [
      .../* @__PURE__ */ new Set([...existingExcludeTools, ...interactiveTools])
    ];
    finalConfig = await loadCliConfig(
      { excludeTools: newExcludeTools },
      extensions,
      config.getSessionId()
    );
  }
  return await validateNonInterActiveAuth(
    config.getSelectedAuthType(),
    finalConfig
  );
}
async function validateNonInterActiveAuth(selectedAuthType, nonInteractiveConfig) {
  if (!selectedAuthType && !process.env.GEMINI_API_KEY) {
    console.error(
      `Please set an Auth method in your ${USER_SETTINGS_PATH} OR specify GEMINI_API_KEY env variable file before running`
    );
    process.exit(1);
  }
  selectedAuthType = selectedAuthType || AuthType.USE_GEMINI;
  const err = validateAuthMethod(selectedAuthType);
  if (err != null) {
    console.error(err);
    process.exit(1);
  }
  await nonInteractiveConfig.refreshAuth(selectedAuthType);
  return nonInteractiveConfig;
}
export {
  main
};
//# sourceMappingURL=arien.js.map
