/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import {
  Tool,
  ToolDefinition,
  ToolResult,
  ToolExecutionContext,
} from './tools.js';
import { getErrorMessage } from '../utils/errors.js';

// Constants for memory discovery
export const GEMINI_CONFIG_DIR = '.gemini';

export function getAllGeminiMdFilenames(): string[] {
  return ['memory.md', 'context.md', 'notes.md'];
}

export function getCurrentGeminiMdFilename(): string {
  return 'memory.md';
}

interface MemoryToolParams {
  action: 'save' | 'load' | 'list' | 'delete' | 'clear';
  key?: string;
  content?: string;
  category?: string;
}

interface MemoryEntry {
  key: string;
  content: string;
  category: string;
  timestamp: string;
  size: number;
}

interface MemoryStore {
  entries: Record<string, MemoryEntry>;
  metadata: {
    created: string;
    lastModified: string;
    totalEntries: number;
  };
}

export class MemoryTool extends Tool<MemoryToolParams> {
  static readonly Name = 'memory';
  private readonly memoryDir: string;
  private readonly memoryFile: string;

  constructor() {
    super(
      MemoryTool.Name,
      'Save, load, and manage persistent memory across sessions',
    );

    // Set up memory directory
    this.memoryDir = path.join(os.homedir(), '.arien', 'memory');
    this.memoryFile = path.join(this.memoryDir, 'memory.json');
    this.ensureMemoryDirectory();
  }

  getDefinition(): ToolDefinition {
    return {
      name: this.name,
      description: this.description,
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['save', 'load', 'list', 'delete', 'clear'],
            description: 'The memory action to perform',
          },
          key: {
            type: 'string',
            description: 'Memory key (required for save, load, delete actions)',
          },
          content: {
            type: 'string',
            description: 'Content to save (required for save action)',
          },
          category: {
            type: 'string',
            description: 'Category for organizing memories (optional)',
            default: 'general',
          },
        },
        required: ['action'],
      },
    };
  }

  private ensureMemoryDirectory(): void {
    if (!fs.existsSync(this.memoryDir)) {
      fs.mkdirSync(this.memoryDir, { recursive: true });
    }
  }

  private loadMemoryStore(): MemoryStore {
    if (!fs.existsSync(this.memoryFile)) {
      return {
        entries: {},
        metadata: {
          created: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          totalEntries: 0,
        },
      };
    }

    try {
      const data = fs.readFileSync(this.memoryFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.warn(
        'Failed to load memory store, creating new one:',
        getErrorMessage(error),
      );
      return {
        entries: {},
        metadata: {
          created: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          totalEntries: 0,
        },
      };
    }
  }

  private saveMemoryStore(store: MemoryStore): void {
    store.metadata.lastModified = new Date().toISOString();
    store.metadata.totalEntries = Object.keys(store.entries).length;

    try {
      fs.writeFileSync(this.memoryFile, JSON.stringify(store, null, 2), 'utf8');
    } catch (error) {
      throw new Error(`Failed to save memory store: ${getErrorMessage(error)}`);
    }
  }

  private validateKey(key: string): void {
    if (!key || key.trim().length === 0) {
      throw new Error('Memory key cannot be empty');
    }

    if (key.length > 100) {
      throw new Error('Memory key is too long (max 100 characters)');
    }

    // Check for invalid characters
    if (!/^[a-zA-Z0-9_\-\.]+$/.test(key)) {
      throw new Error(
        'Memory key can only contain letters, numbers, underscores, hyphens, and dots',
      );
    }
  }

  private formatMemoryList(entries: Record<string, MemoryEntry>): string {
    const entryList = Object.values(entries);

    if (entryList.length === 0) {
      return 'No memories stored.';
    }

    // Group by category
    const byCategory = new Map<string, MemoryEntry[]>();
    for (const entry of entryList) {
      const category = entry.category || 'general';
      if (!byCategory.has(category)) {
        byCategory.set(category, []);
      }
      byCategory.get(category)!.push(entry);
    }

    let output = `Total memories: ${entryList.length}\n\n`;

    // Sort categories
    const sortedCategories = Array.from(byCategory.keys()).sort();

    for (const category of sortedCategories) {
      const categoryEntries = byCategory.get(category)!;
      output += `📁 ${category} (${categoryEntries.length} ${categoryEntries.length === 1 ? 'entry' : 'entries'})\n`;

      // Sort entries by timestamp (newest first)
      categoryEntries.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
      );

      for (const entry of categoryEntries) {
        const date = new Date(entry.timestamp).toLocaleDateString();
        const sizeKB = (entry.size / 1024).toFixed(1);
        const preview =
          entry.content.length > 100
            ? entry.content.substring(0, 100) + '...'
            : entry.content;

        output += `  📝 ${entry.key} (${sizeKB}KB, ${date})\n`;
        output += `     ${preview.replace(/\n/g, ' ')}\n`;
      }
      output += '\n';
    }

    return output.trim();
  }

  async execute(
    params: MemoryToolParams,
    context: ToolExecutionContext,
  ): Promise<ToolResult> {
    try {
      const store = this.loadMemoryStore();

      switch (params.action) {
        case 'save': {
          if (!params.key) {
            return this.createErrorResult('Key is required for save action');
          }
          if (!params.content) {
            return this.createErrorResult(
              'Content is required for save action',
            );
          }

          this.validateKey(params.key);

          const entry: MemoryEntry = {
            key: params.key,
            content: params.content,
            category: params.category || 'general',
            timestamp: new Date().toISOString(),
            size: Buffer.byteLength(params.content, 'utf8'),
          };

          store.entries[params.key] = entry;
          this.saveMemoryStore(store);

          return this.createSuccessResult(
            `Memory saved: "${params.key}" (${(entry.size / 1024).toFixed(1)}KB)`,
            { key: params.key, category: entry.category, size: entry.size },
          );
        }

        case 'load': {
          if (!params.key) {
            return this.createErrorResult('Key is required for load action');
          }

          const entry = store.entries[params.key];
          if (!entry) {
            return this.createErrorResult(`Memory not found: "${params.key}"`);
          }

          const date = new Date(entry.timestamp).toLocaleString();
          let output = `Memory: "${entry.key}"\n`;
          output += `Category: ${entry.category}\n`;
          output += `Saved: ${date}\n`;
          output += `Size: ${(entry.size / 1024).toFixed(1)}KB\n\n`;
          output += `Content:\n${entry.content}`;

          return this.createSuccessResult(output, {
            key: entry.key,
            content: entry.content,
            category: entry.category,
            timestamp: entry.timestamp,
            size: entry.size,
          });
        }

        case 'list': {
          const output = this.formatMemoryList(store.entries);
          return this.createSuccessResult(output, {
            totalEntries: Object.keys(store.entries).length,
            categories: [
              ...new Set(Object.values(store.entries).map(e => e.category)),
            ],
          });
        }

        case 'delete': {
          if (!params.key) {
            return this.createErrorResult('Key is required for delete action');
          }

          if (!store.entries[params.key]) {
            return this.createErrorResult(`Memory not found: "${params.key}"`);
          }

          delete store.entries[params.key];
          this.saveMemoryStore(store);

          return this.createSuccessResult(`Memory deleted: "${params.key}"`);
        }

        case 'clear': {
          const count = Object.keys(store.entries).length;
          store.entries = {};
          this.saveMemoryStore(store);

          return this.createSuccessResult(
            `Cleared ${count} ${count === 1 ? 'memory' : 'memories'}`,
          );
        }

        default:
          return this.createErrorResult(`Unknown action: ${params.action}`);
      }
    } catch (error) {
      return this.createErrorResult(getErrorMessage(error));
    }
  }
}
