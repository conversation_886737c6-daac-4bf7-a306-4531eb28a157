/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback, useRef, useEffect } from "react";
import {
  createContentGenerator,
  createContentGeneratorConfig,
  AuthType,
  executeToolCall
} from "@arien/arien-cli-core";
function getResponseText(response) {
  if (response.candidates && response.candidates.length > 0) {
    const candidate = response.candidates[0];
    if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
      const thoughtPart = candidate.content.parts[0];
      if (thoughtPart?.thought) {
        return null;
      }
      return candidate.content.parts.filter((part) => part.text).map((part) => part.text).join("");
    }
  }
  return null;
}
const useChat = (config) => {
  const [messages, setMessages] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState();
  const contentGeneratorRef = useRef(null);
  const turnCounterRef = useRef(0);
  useEffect(() => {
    const initializeGenerator = async () => {
      try {
        const configData = config.getContentGeneratorConfig();
        if (!configData) {
          throw new Error("No content generator configuration found");
        }
        const mappedAuthType = configData.authType || AuthType.USE_GEMINI;
        const generatorConfig = await createContentGeneratorConfig(
          config.getModel(),
          configData.apiKey,
          mappedAuthType
        );
        const generator = await createContentGenerator(generatorConfig);
        contentGeneratorRef.current = generator;
        setError(void 0);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to initialize content generator";
        setError(errorMessage);
        console.error("Failed to initialize content generator:", err);
      }
    };
    initializeGenerator();
  }, [config]);
  const generateId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }, []);
  const sendMessage = useCallback(
    async (content, approvalCallback) => {
      if (!content.trim() || isGenerating) {
        return;
      }
      if (!contentGeneratorRef.current) {
        setError("Content generator not initialized");
        return;
      }
      const userMessage = {
        id: generateId(),
        role: "user",
        content: content.trim(),
        timestamp: /* @__PURE__ */ new Date()
      };
      setMessages((prev) => [...prev, userMessage]);
      setIsGenerating(true);
      setError(void 0);
      try {
        const geminiClient = config.getGeminiClient();
        const toolRegistry = await config.getToolRegistry();
        const chat = await geminiClient.getChat();
        let currentMessages = [{
          role: "user",
          parts: [{ text: content.trim() }]
        }];
        while (true) {
          const functionCalls = [];
          const responseStream = await chat.sendMessageStream({
            message: currentMessages[0]?.parts || [],
            config: {
              tools: [
                { functionDeclarations: toolRegistry.getFunctionDeclarations() }
              ]
            }
          });
          let responseText = "";
          for await (const resp of responseStream) {
            const textPart = getResponseText(resp);
            if (textPart) {
              responseText += textPart;
            }
            if (resp.functionCalls) {
              functionCalls.push(...resp.functionCalls);
            }
          }
          if (responseText.trim()) {
            const assistantMessage = {
              id: generateId(),
              role: "assistant",
              content: responseText,
              timestamp: /* @__PURE__ */ new Date()
            };
            setMessages((prev) => [...prev, assistantMessage]);
          }
          if (functionCalls.length > 0) {
            const toolResponseParts = [];
            for (const fc of functionCalls) {
              const callId = fc.id ?? `${fc.name}-${Date.now()}`;
              const requestInfo = {
                callId,
                name: fc.name,
                args: fc.args || {}
              };
              try {
                let result;
                const tool = await toolRegistry.get(fc.name);
                if (tool && approvalCallback) {
                  const toolDefinition = toolRegistry.getToolDefinition(fc.name);
                  const riskLevel = toolDefinition?.riskLevel || "low";
                  const approvalRequest = {
                    toolName: fc.name,
                    parameters: fc.args || {},
                    description: toolDefinition?.description || `Execute ${fc.name}`,
                    riskLevel
                  };
                  const approval = await approvalCallback(approvalRequest);
                  if (!approval.approved) {
                    toolResponseParts.push({
                      functionResponse: {
                        name: fc.name,
                        response: {
                          error: `Tool execution denied: ${approval.reason || "User declined"}`
                        }
                      }
                    });
                    const denialMessage = {
                      id: generateId(),
                      role: "assistant",
                      content: `\u{1F6AB} Tool execution denied: ${fc.name} - ${approval.reason || "User declined"}`,
                      timestamp: /* @__PURE__ */ new Date()
                    };
                    setMessages((prev) => [...prev, denialMessage]);
                    continue;
                  }
                }
                result = await executeToolCall(
                  requestInfo,
                  toolRegistry,
                  config,
                  void 0
                  // abortSignal
                );
                toolResponseParts.push({
                  functionResponse: {
                    name: fc.name,
                    response: result
                  }
                });
                const toolMessage = {
                  id: generateId(),
                  role: "assistant",
                  content: `\u{1F527} Executed tool: ${fc.name}`,
                  timestamp: /* @__PURE__ */ new Date()
                };
                setMessages((prev) => [...prev, toolMessage]);
              } catch (error2) {
                const errorMsg = error2 instanceof Error ? error2.message : String(error2);
                toolResponseParts.push({
                  functionResponse: {
                    name: fc.name,
                    response: {
                      error: errorMsg
                    }
                  }
                });
                const toolErrorMessage = {
                  id: generateId(),
                  role: "assistant",
                  content: `\u274C Tool error (${fc.name}): ${errorMsg}`,
                  timestamp: /* @__PURE__ */ new Date(),
                  error: errorMsg
                };
                setMessages((prev) => [...prev, toolErrorMessage]);
              }
            }
            currentMessages = [
              {
                role: "model",
                parts: functionCalls.map((fc) => ({
                  functionCall: {
                    name: fc.name,
                    args: fc.args || {}
                  }
                }))
              },
              {
                role: "user",
                parts: toolResponseParts
              }
            ];
          } else {
            break;
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to generate response";
        setError(errorMessage);
        const errorChatMessage = {
          id: generateId(),
          role: "assistant",
          content: `\u274C Error: ${errorMessage}`,
          timestamp: /* @__PURE__ */ new Date(),
          error: errorMessage
        };
        setMessages((prev) => [...prev, errorChatMessage]);
      } finally {
        setIsGenerating(false);
      }
    },
    [config, isGenerating, generateId]
  );
  const clearChat = useCallback(() => {
    setMessages([]);
    setError(void 0);
    turnCounterRef.current = 0;
  }, []);
  return {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error
  };
};
export {
  useChat
};
//# sourceMappingURL=useChat.js.map
