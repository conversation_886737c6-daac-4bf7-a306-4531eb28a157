import { jsx, jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useCallback } from "react";
import { Box } from "ink";
import { MessageList } from "./MessageList.js";
import { InputBox } from "./InputBox.js";
import { LoadingSpinner } from "./LoadingSpinner.js";
import { ToolApproval } from "./ToolApproval.js";
import { useToolExecution } from "../hooks/useToolExecution.js";
const ChatInterface = ({
  messages,
  isGenerating,
  onSendMessage,
  onClear,
  config
}) => {
  const [inputValue, setInputValue] = useState("");
  const {
    pendingApproval,
    approveTool,
    denyTool,
    approveAllTools,
    denyAllTools,
    getApprovalCallback
  } = useToolExecution(config);
  const handleSubmit = useCallback(
    async (message) => {
      if (!message.trim() || isGenerating) {
        return;
      }
      const trimmedMessage = message.trim();
      if (trimmedMessage === "/clear") {
        onClear();
        return;
      }
      if (trimmedMessage === "/help") {
        const helpMessage = `
Available commands:
- /clear - Clear chat history
- /help - Show this help message
- /quit - Exit the application

Keyboard shortcuts:
- Ctrl+C - Exit
- Ctrl+L - Clear chat
- Ctrl+H - Toggle help
- Tab - Autocomplete (when available)
- \u2191/\u2193 - Navigate command history
      `.trim();
        await onSendMessage(`Help:
${helpMessage}`, getApprovalCallback());
        return;
      }
      if (trimmedMessage === "/quit") {
        process.exit(0);
      }
      await onSendMessage(message, getApprovalCallback());
      setInputValue("");
    },
    [isGenerating, onSendMessage, onClear, getApprovalCallback]
  );
  const handleInputChange = useCallback((value) => {
    setInputValue(value);
  }, []);
  if (pendingApproval) {
    return /* @__PURE__ */ jsx(
      ToolApproval,
      {
        request: pendingApproval,
        onApprove: approveTool,
        onDeny: denyTool,
        onApproveAll: approveAllTools,
        onDenyAll: denyAllTools
      }
    );
  }
  return /* @__PURE__ */ jsxs(Box, { flexDirection: "column", height: "100%", children: [
    /* @__PURE__ */ jsxs(Box, { flexGrow: 1, flexDirection: "column", minHeight: 0, children: [
      /* @__PURE__ */ jsx(MessageList, { messages }),
      isGenerating && /* @__PURE__ */ jsx(Box, { paddingX: 2, paddingY: 1, children: /* @__PURE__ */ jsx(
        LoadingSpinner,
        {
          text: "Generating response",
          color: "yellow",
          type: "dots"
        }
      ) })
    ] }),
    /* @__PURE__ */ jsx(Box, { flexShrink: 0, children: /* @__PURE__ */ jsx(
      InputBox,
      {
        value: inputValue,
        onChange: handleInputChange,
        onSubmit: handleSubmit,
        disabled: isGenerating,
        placeholder: isGenerating ? "Generating response..." : "Type your message..."
      }
    ) })
  ] });
};
export {
  ChatInterface
};
//# sourceMappingURL=ChatInterface.js.map
