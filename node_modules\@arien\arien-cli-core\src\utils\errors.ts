/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export class ArienError extends Error {
  public readonly code: string;
  public readonly context?: Record<string, any>;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    context?: Record<string, any>,
  ) {
    super(message);
    this.name = 'ArienError';
    this.code = code;
    this.context = context;
  }
}

export class ConfigurationError extends ArienError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'CONFIGURATION_ERROR', context);
    this.name = 'ConfigurationError';
  }
}

export class AuthenticationError extends ArienError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'AUTHENTICATION_ERROR', context);
    this.name = 'AuthenticationError';
  }
}

export class ToolExecutionError extends ArienError {
  public readonly toolName: string;

  constructor(
    message: string,
    toolName: string,
    context?: Record<string, any>,
  ) {
    super(message, 'TOOL_EXECUTION_ERROR', context);
    this.name = 'ToolExecutionError';
    this.toolName = toolName;
  }
}

export class FileSystemError extends ArienError {
  public readonly path: string;
  public readonly operation: string;

  constructor(
    message: string,
    path: string,
    operation: string,
    context?: Record<string, any>,
  ) {
    super(message, 'FILESYSTEM_ERROR', context);
    this.name = 'FileSystemError';
    this.path = path;
    this.operation = operation;
  }
}

export class NetworkError extends ArienError {
  public readonly url?: string;
  public readonly statusCode?: number;

  constructor(
    message: string,
    url?: string,
    statusCode?: number,
    context?: Record<string, any>,
  ) {
    super(message, 'NETWORK_ERROR', context);
    this.name = 'NetworkError';
    this.url = url;
    this.statusCode = statusCode;
  }
}

export class ValidationError extends ArienError {
  public readonly field?: string;

  constructor(message: string, field?: string, context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', context);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class RateLimitError extends ArienError {
  public readonly retryAfter?: number;

  constructor(
    message: string,
    retryAfter?: number,
    context?: Record<string, any>,
  ) {
    super(message, 'RATE_LIMIT_ERROR', context);
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

export class TimeoutError extends ArienError {
  public readonly timeout: number;

  constructor(message: string, timeout: number, context?: Record<string, any>) {
    super(message, 'TIMEOUT_ERROR', context);
    this.name = 'TimeoutError';
    this.timeout = timeout;
  }
}

// Error handling utilities
export function isArienError(error: unknown): error is ArienError {
  return error instanceof ArienError;
}

export function isNodeError(error: unknown): error is NodeJS.ErrnoException {
  return error instanceof Error && 'code' in error && 'errno' in error;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export function getErrorCode(error: unknown): string {
  if (isArienError(error)) {
    return error.code;
  }
  if (error instanceof Error) {
    return error.name;
  }
  return 'UNKNOWN_ERROR';
}

export function formatError(error: unknown): string {
  if (isArienError(error)) {
    let formatted = `[${error.code}] ${error.message}`;
    if (error.context) {
      formatted += ` (Context: ${JSON.stringify(error.context)})`;
    }
    return formatted;
  }

  if (error instanceof Error) {
    return `[${error.name}] ${error.message}`;
  }

  return `[UNKNOWN_ERROR] ${String(error)}`;
}

export function createErrorContext(
  additionalContext?: Record<string, any>,
): Record<string, any> {
  return {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    ...additionalContext,
  };
}

// Error recovery utilities
export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: unknown) => boolean;
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {},
): Promise<T> {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryCondition = () => true,
  } = options;

  let lastError: unknown;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxAttempts || !retryCondition(error)) {
        throw error;
      }

      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt - 1),
        maxDelay,
      );
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

export function shouldRetryError(error: unknown): boolean {
  if (isArienError(error)) {
    // Don't retry validation or configuration errors
    return ![
      'VALIDATION_ERROR',
      'CONFIGURATION_ERROR',
      'AUTHENTICATION_ERROR',
    ].includes(error.code);
  }

  if (error instanceof Error) {
    // Don't retry syntax errors or type errors
    return !['SyntaxError', 'TypeError', 'ReferenceError'].includes(error.name);
  }

  return true;
}

// Error reporting utilities
export interface ErrorReport {
  error: string;
  code: string;
  timestamp: string;
  context?: Record<string, any>;
  stack?: string;
}

export function createErrorReport(
  error: unknown,
  additionalContext?: Record<string, any>,
): ErrorReport {
  const report: ErrorReport = {
    error: getErrorMessage(error),
    code: getErrorCode(error),
    timestamp: new Date().toISOString(),
  };

  if (error instanceof Error && error.stack) {
    report.stack = error.stack;
  }

  if (isArienError(error) && error.context) {
    report.context = { ...error.context, ...additionalContext };
  } else if (additionalContext) {
    report.context = additionalContext;
  }

  return report;
}

// Graceful error handling for async operations
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallback?: T,
  onError?: (error: unknown) => void,
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    if (onError) {
      onError(error);
    }
    return fallback;
  }
}

export function safeSync<T>(
  operation: () => T,
  fallback?: T,
  onError?: (error: unknown) => void,
): T | undefined {
  try {
    return operation();
  } catch (error) {
    if (onError) {
      onError(error);
    }
    return fallback;
  }
}
