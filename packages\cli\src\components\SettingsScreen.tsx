/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Config } from '@arien/arien-cli-core';

interface SettingsScreenProps {
  config: Config;
  configErrors: string[];
  isConfigValid: boolean;
}

export const SettingsScreen: React.FC<SettingsScreenProps> = ({
  config,
  configErrors,
  isConfigValid,
}) => {
  const getStatusIcon = (isValid: boolean) => {
    return isValid ? '✅' : '❌';
  };

  const getStatusColor = (isValid: boolean) => {
    return isValid ? 'green' : 'red';
  };

  return (
    <Box flexDirection="column" paddingX={2} paddingY={1}>
      <Text bold color="cyan">
        ⚙️ Configuration Settings
      </Text>
      
      <Box flexDirection="column" marginTop={1}>
        {/* Overall Configuration Status */}
        <Box>
          <Text color={getStatusColor(isConfigValid)}>
            {getStatusIcon(isConfigValid)} Configuration Status: {isConfigValid ? 'Valid' : 'Invalid'}
          </Text>
        </Box>

        {/* Configuration Errors */}
        {configErrors.length > 0 && (
          <Box flexDirection="column" marginTop={1}>
            <Text color="red" bold>
              ❌ Configuration Errors:
            </Text>
            {configErrors.map((error, index) => (
              <Box key={index} marginLeft={2}>
                <Text color="red">• {error}</Text>
              </Box>
            ))}
          </Box>
        )}

        {/* Current Configuration Details */}
        <Box flexDirection="column" marginTop={2}>
          <Text color="yellow" bold>
            📋 Current Configuration:
          </Text>
          
          <Box marginTop={1}>
            <Text color="gray">Model: </Text>
            <Text color="white">{config.getModel() || 'Not set'}</Text>
          </Box>
          
          <Box>
            <Text color="gray">Workspace Root: </Text>
            <Text color="white">{config.getWorkspaceRoot() || 'Not set'}</Text>
          </Box>
          
          <Box>
            <Text color="gray">Debug Mode: </Text>
            <Text color="white">{config.getDebugMode() ? 'Enabled' : 'Disabled'}</Text>
          </Box>
          
          <Box>
            <Text color="gray">All Files: </Text>
            <Text color="white">{config.getAllFiles() ? 'Enabled' : 'Disabled'}</Text>
          </Box>
          
          <Box>
            <Text color="gray">Approval Mode: </Text>
            <Text color="white">{config.getApprovalMode()}</Text>
          </Box>
          
          <Box>
            <Text color="gray">Auth Type: </Text>
            <Text color="white">{config.getSelectedAuthType() || 'Not set'}</Text>
          </Box>
        </Box>

        {/* Settings File Errors */}
        {(() => {
          const settingsErrors = config.getSettingsErrors();
          if (settingsErrors.length > 0) {
            return (
              <Box flexDirection="column" marginTop={2}>
                <Text color="red" bold>
                  📄 Settings File Errors:
                </Text>
                {settingsErrors.map((error, index) => (
                  <Box key={index} marginLeft={2}>
                    <Text color="red">• {error.path}: {error.message}</Text>
                  </Box>
                ))}
              </Box>
            );
          }
          return null;
        })()}

        {/* Help Text */}
        <Box flexDirection="column" marginTop={2}>
          <Text color="cyan" bold>
            💡 Help:
          </Text>
          <Text color="gray">
            • Configuration files are located in ~/.arien/settings.json (user) and .arien/settings.json (workspace)
          </Text>
          <Text color="gray">
            • Environment variables can be set in .env files
          </Text>
          <Text color="gray">
            • Use command line arguments to override settings temporarily
          </Text>
        </Box>

        {/* Navigation Help */}
        <Box flexDirection="column" marginTop={2} borderStyle="single" borderColor="gray" padding={1}>
          <Text color="yellow" bold>
            🎮 Navigation:
          </Text>
          <Text color="gray">[C] Start Chat (if config is valid)</Text>
          <Text color="gray">[Q] Quit Application</Text>
          <Text color="gray">[H] Show Help</Text>
          <Text color="gray">[V] Service Status</Text>
        </Box>
      </Box>
    </Box>
  );
};
