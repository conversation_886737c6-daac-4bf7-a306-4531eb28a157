{"version": 3, "sources": ["../../../src/components/SettingsScreen.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React from 'react';\nimport { Box, Text } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\n\ninterface SettingsScreenProps {\n  config: Config;\n  configErrors: string[];\n  isConfigValid: boolean;\n}\n\nexport const SettingsScreen: React.FC<SettingsScreenProps> = ({\n  config,\n  configErrors,\n  isConfigValid,\n}) => {\n  const getStatusIcon = (isValid: boolean) => {\n    return isValid ? '✅' : '❌';\n  };\n\n  const getStatusColor = (isValid: boolean) => {\n    return isValid ? 'green' : 'red';\n  };\n\n  return (\n    <Box flexDirection=\"column\" paddingX={2} paddingY={1}>\n      <Text bold color=\"cyan\">\n        ⚙️ Configuration Settings\n      </Text>\n      \n      <Box flexDirection=\"column\" marginTop={1}>\n        {/* Overall Configuration Status */}\n        <Box>\n          <Text color={getStatusColor(isConfigValid)}>\n            {getStatusIcon(isConfigValid)} Configuration Status: {isConfigValid ? 'Valid' : 'Invalid'}\n          </Text>\n        </Box>\n\n        {/* Configuration Errors */}\n        {configErrors.length > 0 && (\n          <Box flexDirection=\"column\" marginTop={1}>\n            <Text color=\"red\" bold>\n              ❌ Configuration Errors:\n            </Text>\n            {configErrors.map((error, index) => (\n              <Box key={index} marginLeft={2}>\n                <Text color=\"red\">• {error}</Text>\n              </Box>\n            ))}\n          </Box>\n        )}\n\n        {/* Current Configuration Details */}\n        <Box flexDirection=\"column\" marginTop={2}>\n          <Text color=\"yellow\" bold>\n            📋 Current Configuration:\n          </Text>\n          \n          <Box marginTop={1}>\n            <Text color=\"gray\">Model: </Text>\n            <Text color=\"white\">{config.getModel() || 'Not set'}</Text>\n          </Box>\n          \n          <Box>\n            <Text color=\"gray\">Workspace Root: </Text>\n            <Text color=\"white\">{config.getWorkspaceRoot() || 'Not set'}</Text>\n          </Box>\n          \n          <Box>\n            <Text color=\"gray\">Debug Mode: </Text>\n            <Text color=\"white\">{config.getDebugMode() ? 'Enabled' : 'Disabled'}</Text>\n          </Box>\n          \n          <Box>\n            <Text color=\"gray\">All Files: </Text>\n            <Text color=\"white\">{config.getAllFiles() ? 'Enabled' : 'Disabled'}</Text>\n          </Box>\n          \n          <Box>\n            <Text color=\"gray\">Approval Mode: </Text>\n            <Text color=\"white\">{config.getApprovalMode()}</Text>\n          </Box>\n          \n          <Box>\n            <Text color=\"gray\">Auth Type: </Text>\n            <Text color=\"white\">{config.getSelectedAuthType() || 'Not set'}</Text>\n          </Box>\n        </Box>\n\n        {/* Settings File Errors */}\n        {(() => {\n          const settingsErrors = config.getSettingsErrors();\n          if (settingsErrors.length > 0) {\n            return (\n              <Box flexDirection=\"column\" marginTop={2}>\n                <Text color=\"red\" bold>\n                  📄 Settings File Errors:\n                </Text>\n                {settingsErrors.map((error, index) => (\n                  <Box key={index} marginLeft={2}>\n                    <Text color=\"red\">• {error.path}: {error.message}</Text>\n                  </Box>\n                ))}\n              </Box>\n            );\n          }\n          return null;\n        })()}\n\n        {/* Help Text */}\n        <Box flexDirection=\"column\" marginTop={2}>\n          <Text color=\"cyan\" bold>\n            💡 Help:\n          </Text>\n          <Text color=\"gray\">\n            • Configuration files are located in ~/.arien/settings.json (user) and .arien/settings.json (workspace)\n          </Text>\n          <Text color=\"gray\">\n            • Environment variables can be set in .env files\n          </Text>\n          <Text color=\"gray\">\n            • Use command line arguments to override settings temporarily\n          </Text>\n        </Box>\n\n        {/* Navigation Help */}\n        <Box flexDirection=\"column\" marginTop={2} borderStyle=\"single\" borderColor=\"gray\" padding={1}>\n          <Text color=\"yellow\" bold>\n            🎮 Navigation:\n          </Text>\n          <Text color=\"gray\">[C] Start Chat (if config is valid)</Text>\n          <Text color=\"gray\">[Q] Quit Application</Text>\n          <Text color=\"gray\">[H] Show Help</Text>\n          <Text color=\"gray\">[V] Service Status</Text>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AA+BM,cAOI,YAPJ;AA/BN;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,KAAK,YAAY;AASnB,MAAM,iBAAgD,CAAC;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,gBAAgB,CAAC,YAAqB;AAC1C,WAAO,UAAU,WAAM;AAAA,EACzB;AAEA,QAAM,iBAAiB,CAAC,YAAqB;AAC3C,WAAO,UAAU,UAAU;AAAA,EAC7B;AAEA,SACE,qBAAC,OAAI,eAAc,UAAS,UAAU,GAAG,UAAU,GACjD;AAAA,wBAAC,QAAK,MAAI,MAAC,OAAM,QAAO,iDAExB;AAAA,IAEA,qBAAC,OAAI,eAAc,UAAS,WAAW,GAErC;AAAA,0BAAC,OACC,+BAAC,QAAK,OAAO,eAAe,aAAa,GACtC;AAAA,sBAAc,aAAa;AAAA,QAAE;AAAA,QAAwB,gBAAgB,UAAU;AAAA,SAClF,GACF;AAAA,MAGC,aAAa,SAAS,KACrB,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,4BAAC,QAAK,OAAM,OAAM,MAAI,MAAC,0CAEvB;AAAA,QACC,aAAa,IAAI,CAAC,OAAO,UACxB,oBAAC,OAAgB,YAAY,GAC3B,+BAAC,QAAK,OAAM,OAAM;AAAA;AAAA,UAAG;AAAA,WAAM,KADnB,KAEV,CACD;AAAA,SACH;AAAA,MAIF,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,4BAAC,QAAK,OAAM,UAAS,MAAI,MAAC,8CAE1B;AAAA,QAEA,qBAAC,OAAI,WAAW,GACd;AAAA,8BAAC,QAAK,OAAM,QAAO,qBAAO;AAAA,UAC1B,oBAAC,QAAK,OAAM,SAAS,iBAAO,SAAS,KAAK,WAAU;AAAA,WACtD;AAAA,QAEA,qBAAC,OACC;AAAA,8BAAC,QAAK,OAAM,QAAO,8BAAgB;AAAA,UACnC,oBAAC,QAAK,OAAM,SAAS,iBAAO,iBAAiB,KAAK,WAAU;AAAA,WAC9D;AAAA,QAEA,qBAAC,OACC;AAAA,8BAAC,QAAK,OAAM,QAAO,0BAAY;AAAA,UAC/B,oBAAC,QAAK,OAAM,SAAS,iBAAO,aAAa,IAAI,YAAY,YAAW;AAAA,WACtE;AAAA,QAEA,qBAAC,OACC;AAAA,8BAAC,QAAK,OAAM,QAAO,yBAAW;AAAA,UAC9B,oBAAC,QAAK,OAAM,SAAS,iBAAO,YAAY,IAAI,YAAY,YAAW;AAAA,WACrE;AAAA,QAEA,qBAAC,OACC;AAAA,8BAAC,QAAK,OAAM,QAAO,6BAAe;AAAA,UAClC,oBAAC,QAAK,OAAM,SAAS,iBAAO,gBAAgB,GAAE;AAAA,WAChD;AAAA,QAEA,qBAAC,OACC;AAAA,8BAAC,QAAK,OAAM,QAAO,yBAAW;AAAA,UAC9B,oBAAC,QAAK,OAAM,SAAS,iBAAO,oBAAoB,KAAK,WAAU;AAAA,WACjE;AAAA,SACF;AAAA,OAGE,MAAM;AACN,cAAM,iBAAiB,OAAO,kBAAkB;AAChD,YAAI,eAAe,SAAS,GAAG;AAC7B,iBACE,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,gCAAC,QAAK,OAAM,OAAM,MAAI,MAAC,6CAEvB;AAAA,YACC,eAAe,IAAI,CAAC,OAAO,UAC1B,oBAAC,OAAgB,YAAY,GAC3B,+BAAC,QAAK,OAAM,OAAM;AAAA;AAAA,cAAG,MAAM;AAAA,cAAK;AAAA,cAAG,MAAM;AAAA,eAAQ,KADzC,KAEV,CACD;AAAA,aACH;AAAA,QAEJ;AACA,eAAO;AAAA,MACT,GAAG;AAAA,MAGH,qBAAC,OAAI,eAAc,UAAS,WAAW,GACrC;AAAA,4BAAC,QAAK,OAAM,QAAO,MAAI,MAAC,6BAExB;AAAA,QACA,oBAAC,QAAK,OAAM,QAAO,0HAEnB;AAAA,QACA,oBAAC,QAAK,OAAM,QAAO,mEAEnB;AAAA,QACA,oBAAC,QAAK,OAAM,QAAO,gFAEnB;AAAA,SACF;AAAA,MAGA,qBAAC,OAAI,eAAc,UAAS,WAAW,GAAG,aAAY,UAAS,aAAY,QAAO,SAAS,GACzF;AAAA,4BAAC,QAAK,OAAM,UAAS,MAAI,MAAC,mCAE1B;AAAA,QACA,oBAAC,QAAK,OAAM,QAAO,iDAAmC;AAAA,QACtD,oBAAC,QAAK,OAAM,QAAO,kCAAoB;AAAA,QACvC,oBAAC,QAAK,OAAM,QAAO,2BAAa;AAAA,QAChC,oBAAC,QAAK,OAAM,QAAO,gCAAkB;AAAA,SACvC;AAAA,OACF;AAAA,KACF;AAEJ;", "names": []}