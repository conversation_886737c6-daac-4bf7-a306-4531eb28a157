/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  Config,
  ContentGenerator,
  Turn,
  createContentGenerator,
  createContentGeneratorConfig,
  Auth<PERSON>ype,
  <PERSON>lRegistry,
  ToolCallRequestInfo,
  executeToolCall,
} from '@arien/arien-cli-core';
import {
  Content,
  Part,
  FunctionCall,
  GenerateContentResponse,
} from '@google/genai';

// Tool approval types (defined locally since they're not exported from core)
export interface ToolApprovalRequest {
  toolName: string;
  parameters: any;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ToolApproval {
  approved: boolean;
  reason?: string;
  rememberChoice?: boolean;
}

// Helper function to extract text from AI response
function getResponseText(response: GenerateContentResponse): string | null {
  if (response.candidates && response.candidates.length > 0) {
    const candidate = response.candidates[0];
    if (
      candidate.content &&
      candidate.content.parts &&
      candidate.content.parts.length > 0
    ) {
      // Filter out thought parts for interactive mode
      const thoughtPart = candidate.content.parts[0];
      if (thoughtPart?.thought) {
        return null;
      }
      return candidate.content.parts
        .filter((part) => part.text)
        .map((part) => part.text)
        .join('');
    }
  }
  return null;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  turn?: Turn;
  error?: string;
}

export interface UseChatReturn {
  messages: ChatMessage[];
  isGenerating: boolean;
  sendMessage: (content: string, approvalCallback?: (request: ToolApprovalRequest) => Promise<ToolApproval>) => Promise<void>;
  clearChat: () => void;
  error?: string;
}

export const useChat = (config: Config): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | undefined>();

  const contentGeneratorRef = useRef<ContentGenerator | null>(null);
  const turnCounterRef = useRef(0);

  // Initialize content generator
  useEffect(() => {
    const initializeGenerator = async () => {
      try {
        const configData = config.getContentGeneratorConfig();
        if (!configData) {
          throw new Error('No content generator configuration found');
        }

        // Use the authType directly since it's now using the unified enum values
        const mappedAuthType: AuthType = configData.authType || AuthType.USE_GEMINI;

        const generatorConfig = await createContentGeneratorConfig(
          config.getModel(),
          configData.apiKey,
          mappedAuthType,
        );

        const generator = await createContentGenerator(generatorConfig);
        contentGeneratorRef.current = generator;
        setError(undefined);
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to initialize content generator';
        setError(errorMessage);
        console.error('Failed to initialize content generator:', err);
      }
    };

    initializeGenerator();
  }, [config]);

  const generateId = useCallback(() => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }, []);

  const sendMessage = useCallback(
    async (content: string, approvalCallback?: (request: ToolApprovalRequest) => Promise<ToolApproval>) => {
      if (!content.trim() || isGenerating) {
        return;
      }

      if (!contentGeneratorRef.current) {
        setError('Content generator not initialized');
        return;
      }

      // Create user message
      const userMessage: ChatMessage = {
        id: generateId(),
        role: 'user',
        content: content.trim(),
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setIsGenerating(true);
      setError(undefined);

      try {
        // Get AI client and tool registry
        const geminiClient = config.getGeminiClient();
        const toolRegistry: ToolRegistry = await config.getToolRegistry();
        const chat = await geminiClient.getChat();

        // Convert messages to Gemini format
        let currentMessages: Content[] = [{
          role: 'user',
          parts: [{ text: content.trim() }]
        }];

        // Main conversation loop with tool execution
        while (true) {
          const functionCalls: FunctionCall[] = [];

          // Send message to AI with tool declarations
          const responseStream = await chat.sendMessageStream({
            message: currentMessages[0]?.parts || [],
            config: {
              tools: [
                { functionDeclarations: toolRegistry.getFunctionDeclarations() },
              ],
            },
          });

          let responseText = '';

          // Process streaming response
          for await (const resp of responseStream) {
            const textPart = getResponseText(resp);
            if (textPart) {
              responseText += textPart;
            }
            if (resp.functionCalls) {
              functionCalls.push(...resp.functionCalls);
            }
          }

          // Add AI response to messages if there's text content
          if (responseText.trim()) {
            const assistantMessage: ChatMessage = {
              id: generateId(),
              role: 'assistant',
              content: responseText,
              timestamp: new Date(),
            };
            setMessages(prev => [...prev, assistantMessage]);
          }

          // Handle function calls if any
          if (functionCalls.length > 0) {
            const toolResponseParts: Part[] = [];

            for (const fc of functionCalls) {
              const callId = fc.id ?? `${fc.name}-${Date.now()}`;
              const requestInfo: ToolCallRequestInfo = {
                callId,
                name: fc.name as string,
                args: fc.args || {},
              };

              try {
                let result;

                // Check if tool requires approval and we have an approval callback
                const tool = await toolRegistry.get(fc.name as string);
                if (tool && approvalCallback) {
                  // Check if this is an approval-required tool
                  const toolDefinition = toolRegistry.getToolDefinition(fc.name as string);
                  const riskLevel = toolDefinition?.riskLevel || 'low';

                  // Create approval request
                  const approvalRequest: ToolApprovalRequest = {
                    toolName: fc.name as string,
                    parameters: fc.args || {},
                    description: toolDefinition?.description || `Execute ${fc.name}`,
                    riskLevel: riskLevel as 'low' | 'medium' | 'high',
                  };

                  // Request approval
                  const approval = await approvalCallback(approvalRequest);

                  if (!approval.approved) {
                    // Tool execution denied
                    toolResponseParts.push({
                      functionResponse: {
                        name: fc.name,
                        response: {
                          error: `Tool execution denied: ${approval.reason || 'User declined'}`,
                        },
                      },
                    });

                    // Add denial message to chat
                    const denialMessage: ChatMessage = {
                      id: generateId(),
                      role: 'assistant',
                      content: `🚫 Tool execution denied: ${fc.name} - ${approval.reason || 'User declined'}`,
                      timestamp: new Date(),
                    };
                    setMessages(prev => [...prev, denialMessage]);
                    continue;
                  }
                }

                // Execute tool (either approved or doesn't require approval)
                result = await executeToolCall(
                  requestInfo,
                  toolRegistry,
                  config,
                  undefined, // abortSignal
                );

                toolResponseParts.push({
                  functionResponse: {
                    name: fc.name,
                    response: result,
                  },
                });

                // Add tool execution message to chat
                const toolMessage: ChatMessage = {
                  id: generateId(),
                  role: 'assistant',
                  content: `🔧 Executed tool: ${fc.name}`,
                  timestamp: new Date(),
                };
                setMessages(prev => [...prev, toolMessage]);
              } catch (error) {
                const errorMsg = error instanceof Error ? error.message : String(error);
                toolResponseParts.push({
                  functionResponse: {
                    name: fc.name,
                    response: {
                      error: errorMsg,
                    },
                  },
                });

                // Add tool error message to chat
                const toolErrorMessage: ChatMessage = {
                  id: generateId(),
                  role: 'assistant',
                  content: `❌ Tool error (${fc.name}): ${errorMsg}`,
                  timestamp: new Date(),
                  error: errorMsg,
                };
                setMessages(prev => [...prev, toolErrorMessage]);
              }
            }

            // Continue conversation with tool responses
            currentMessages = [
              {
                role: 'model',
                parts: functionCalls.map((fc) => ({
                  functionCall: {
                    name: fc.name,
                    args: fc.args || {},
                  },
                })),
              },
              {
                role: 'user',
                parts: toolResponseParts,
              },
            ];
          } else {
            // No more function calls, conversation is complete
            break;
          }
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to generate response';
        setError(errorMessage);

        // Add error message to chat
        const errorChatMessage: ChatMessage = {
          id: generateId(),
          role: 'assistant',
          content: `❌ Error: ${errorMessage}`,
          timestamp: new Date(),
          error: errorMessage,
        };

        setMessages(prev => [...prev, errorChatMessage]);
      } finally {
        setIsGenerating(false);
      }
    },
    [config, isGenerating, generateId],
  );

  const clearChat = useCallback(() => {
    setMessages([]);
    setError(undefined);
    turnCounterRef.current = 0;
  }, []);

  return {
    messages,
    isGenerating,
    sendMessage,
    clearChat,
    error,
  };
};
