{"version": 3, "sources": ["../../../src/core/coreToolScheduler.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport {\n  ToolCallRequestInfo,\n  ToolCallResponseInfo,\n  ToolConfirmationOutcome,\n  Tool,\n  ToolCallConfirmationDetails,\n  ToolResult,\n  ToolRegistry,\n  ApprovalMode,\n  EditorType,\n  Config,\n  logToolCall,\n  ToolCallEvent,\n} from '../index.js';\nimport { Part, PartListUnion } from '@google/genai';\nimport { getResponseTextFromParts } from '../utils/generateContentResponseUtilities.js';\nimport {\n  isModifiableTool,\n  ModifyContext,\n  modifyWithEditor,\n} from '../tools/modifiable-tool.js';\n\nexport type ValidatingToolCall = {\n  status: 'validating';\n  request: ToolCallRequestInfo;\n  tool: Tool;\n  startTime?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type ScheduledToolCall = {\n  status: 'scheduled';\n  request: ToolCallRequestInfo;\n  tool: Tool;\n  startTime?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type ErroredToolCall = {\n  status: 'error';\n  request: ToolCallRequestInfo;\n  response: ToolCallResponseInfo;\n  durationMs?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type SuccessfulToolCall = {\n  status: 'success';\n  request: ToolCallRequestInfo;\n  tool: Tool;\n  response: ToolCallResponseInfo;\n  durationMs?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type ExecutingToolCall = {\n  status: 'executing';\n  request: ToolCallRequestInfo;\n  tool: Tool;\n  liveOutput?: string;\n  startTime?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type CancelledToolCall = {\n  status: 'cancelled';\n  request: ToolCallRequestInfo;\n  response: ToolCallResponseInfo;\n  tool: Tool;\n  durationMs?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type WaitingToolCall = {\n  status: 'awaiting_approval';\n  request: ToolCallRequestInfo;\n  tool: Tool;\n  confirmationDetails: ToolCallConfirmationDetails;\n  startTime?: number;\n  outcome?: ToolConfirmationOutcome;\n};\n\nexport type Status = ToolCall['status'];\n\nexport type ToolCall =\n  | ValidatingToolCall\n  | ScheduledToolCall\n  | ErroredToolCall\n  | SuccessfulToolCall\n  | ExecutingToolCall\n  | CancelledToolCall\n  | WaitingToolCall;\n\nexport type CompletedToolCall =\n  | ErroredToolCall\n  | SuccessfulToolCall\n  | CancelledToolCall;\n\nexport interface ToolSchedulerCallbacks {\n  onToolCallUpdate?: (toolCall: ToolCall) => void;\n  onToolCallComplete?: (toolCall: CompletedToolCall) => void;\n  onAllToolCallsComplete?: (toolCalls: CompletedToolCall[]) => void;\n  onToolCallConfirmation?: (\n    toolCall: WaitingToolCall,\n  ) => Promise<ToolConfirmationOutcome>;\n}\n\n/**\n * Core tool scheduler for managing tool execution\n */\nexport class CoreToolScheduler {\n  private toolCalls: Map<string, ToolCall> = new Map();\n  private callbacks: ToolSchedulerCallbacks;\n  private config: Config;\n  private toolRegistry: ToolRegistry;\n\n  constructor(\n    config: Config,\n    toolRegistry: ToolRegistry,\n    callbacks: ToolSchedulerCallbacks = {},\n  ) {\n    this.config = config;\n    this.toolRegistry = toolRegistry;\n    this.callbacks = callbacks;\n  }\n\n  /**\n   * Schedule tool calls for execution\n   */\n  async scheduleToolCalls(\n    toolCallRequests: ToolCallRequestInfo[],\n  ): Promise<ToolCallResponseInfo[]> {\n    const responses: ToolCallResponseInfo[] = [];\n\n    for (const request of toolCallRequests) {\n      try {\n        const tool = await this.toolRegistry.getTool(request.name);\n        if (!tool) {\n          const errorResponse: ToolCallResponseInfo = {\n            name: request.name,\n            response: {\n              error: `Tool '${request.name}' not found`,\n            },\n          };\n          responses.push(errorResponse);\n          continue;\n        }\n\n        const toolCall: ScheduledToolCall = {\n          status: 'scheduled',\n          request,\n          tool,\n          startTime: Date.now(),\n        };\n\n        this.toolCalls.set(request.name, toolCall);\n        this.callbacks.onToolCallUpdate?.(toolCall);\n\n        const response = await this.executeToolCall(toolCall);\n        responses.push(response);\n      } catch (error) {\n        const errorResponse: ToolCallResponseInfo = {\n          name: request.name,\n          response: {\n            error: error instanceof Error ? error.message : String(error),\n          },\n        };\n        responses.push(errorResponse);\n      }\n    }\n\n    return responses;\n  }\n\n  /**\n   * Execute a single tool call\n   */\n  private async executeToolCall(\n    toolCall: ScheduledToolCall,\n  ): Promise<ToolCallResponseInfo> {\n    const executingToolCall: ExecutingToolCall = {\n      ...toolCall,\n      status: 'executing',\n    };\n\n    this.toolCalls.set(toolCall.request.name, executingToolCall);\n    this.callbacks.onToolCallUpdate?.(executingToolCall);\n\n    try {\n      const result = await toolCall.tool.execute(\n        toolCall.request.args,\n        this.config,\n      );\n\n      const successfulToolCall: SuccessfulToolCall = {\n        ...executingToolCall,\n        status: 'success',\n        response: {\n          name: toolCall.request.name,\n          response: result,\n        },\n        durationMs: Date.now() - (toolCall.startTime || 0),\n      };\n\n      this.toolCalls.set(toolCall.request.name, successfulToolCall);\n      this.callbacks.onToolCallUpdate?.(successfulToolCall);\n      this.callbacks.onToolCallComplete?.(successfulToolCall);\n\n      return successfulToolCall.response;\n    } catch (error) {\n      const erroredToolCall: ErroredToolCall = {\n        ...executingToolCall,\n        status: 'error',\n        response: {\n          name: toolCall.request.name,\n          response: {\n            error: error instanceof Error ? error.message : String(error),\n          },\n        },\n        durationMs: Date.now() - (toolCall.startTime || 0),\n      };\n\n      this.toolCalls.set(toolCall.request.name, erroredToolCall);\n      this.callbacks.onToolCallUpdate?.(erroredToolCall);\n      this.callbacks.onToolCallComplete?.(erroredToolCall);\n\n      return erroredToolCall.response;\n    }\n  }\n\n  /**\n   * Get all tool calls\n   */\n  getToolCalls(): ToolCall[] {\n    return Array.from(this.toolCalls.values());\n  }\n\n  /**\n   * Get a specific tool call by name\n   */\n  getToolCall(name: string): ToolCall | undefined {\n    return this.toolCalls.get(name);\n  }\n\n  /**\n   * Clear all tool calls\n   */\n  clear(): void {\n    this.toolCalls.clear();\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAoHO,MAAM,kBAAkB;AAAA,EACrB,YAAmC,oBAAI,IAAI;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EAER,YACE,QACA,cACA,YAAoC,CAAC,GACrC;AACA,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBACJ,kBACiC;AACjC,UAAM,YAAoC,CAAC;AAE3C,eAAW,WAAW,kBAAkB;AACtC,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,aAAa,QAAQ,QAAQ,IAAI;AACzD,YAAI,CAAC,MAAM;AACT,gBAAM,gBAAsC;AAAA,YAC1C,MAAM,QAAQ;AAAA,YACd,UAAU;AAAA,cACR,OAAO,SAAS,QAAQ,IAAI;AAAA,YAC9B;AAAA,UACF;AACA,oBAAU,KAAK,aAAa;AAC5B;AAAA,QACF;AAEA,cAAM,WAA8B;AAAA,UAClC,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,UACA,WAAW,KAAK,IAAI;AAAA,QACtB;AAEA,aAAK,UAAU,IAAI,QAAQ,MAAM,QAAQ;AACzC,aAAK,UAAU,mBAAmB,QAAQ;AAE1C,cAAM,WAAW,MAAM,KAAK,gBAAgB,QAAQ;AACpD,kBAAU,KAAK,QAAQ;AAAA,MACzB,SAAS,OAAO;AACd,cAAM,gBAAsC;AAAA,UAC1C,MAAM,QAAQ;AAAA,UACd,UAAU;AAAA,YACR,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,UAC9D;AAAA,QACF;AACA,kBAAU,KAAK,aAAa;AAAA,MAC9B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,gBACZ,UAC+B;AAC/B,UAAM,oBAAuC;AAAA,MAC3C,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAEA,SAAK,UAAU,IAAI,SAAS,QAAQ,MAAM,iBAAiB;AAC3D,SAAK,UAAU,mBAAmB,iBAAiB;AAEnD,QAAI;AACF,YAAM,SAAS,MAAM,SAAS,KAAK;AAAA,QACjC,SAAS,QAAQ;AAAA,QACjB,KAAK;AAAA,MACP;AAEA,YAAM,qBAAyC;AAAA,QAC7C,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,MAAM,SAAS,QAAQ;AAAA,UACvB,UAAU;AAAA,QACZ;AAAA,QACA,YAAY,KAAK,IAAI,KAAK,SAAS,aAAa;AAAA,MAClD;AAEA,WAAK,UAAU,IAAI,SAAS,QAAQ,MAAM,kBAAkB;AAC5D,WAAK,UAAU,mBAAmB,kBAAkB;AACpD,WAAK,UAAU,qBAAqB,kBAAkB;AAEtD,aAAO,mBAAmB;AAAA,IAC5B,SAAS,OAAO;AACd,YAAM,kBAAmC;AAAA,QACvC,GAAG;AAAA,QACH,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,MAAM,SAAS,QAAQ;AAAA,UACvB,UAAU;AAAA,YACR,OAAO,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AAAA,UAC9D;AAAA,QACF;AAAA,QACA,YAAY,KAAK,IAAI,KAAK,SAAS,aAAa;AAAA,MAClD;AAEA,WAAK,UAAU,IAAI,SAAS,QAAQ,MAAM,eAAe;AACzD,WAAK,UAAU,mBAAmB,eAAe;AACjD,WAAK,UAAU,qBAAqB,eAAe;AAEnD,aAAO,gBAAgB;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,eAA2B;AACzB,WAAO,MAAM,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAoC;AAC9C,WAAO,KAAK,UAAU,IAAI,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,QAAc;AACZ,SAAK,UAAU,MAAM;AAAA,EACvB;AACF;", "names": []}