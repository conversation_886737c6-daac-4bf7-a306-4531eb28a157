/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Config } from '@arien/arien-cli-core';

interface WelcomeScreenProps {
  config: Config;
  onStartChat: () => void;
  onShowSettings: () => void;
  onShowHelp: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  config,
  onStartChat,
  onShowSettings,
  onShowHelp,
}) => {
  const formatModel = (model: string) => {
    return model
      .replace('gemini-2.0-flash-exp', 'Gemini 2.0 Flash Experimental')
      .replace('gemini-1.5-pro', 'Gemini 1.5 Pro')
      .replace('gemini-1.5-flash', 'Gemini 1.5 Flash');
  };

  return (
    <Box
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      flexGrow={1}
    >
      {/* Logo/Title */}
      <Box flexDirection="column" alignItems="center" marginBottom={2}>
        <Text color="cyan" bold>
          ╔═══════════════════════════════════╗
        </Text>
        <Text color="cyan" bold>
          ║ ║
        </Text>
        <Text color="cyan" bold>
          ║ 🤖 ARIEN ║
        </Text>
        <Text color="cyan" bold>
          ║ ║
        </Text>
        <Text color="cyan" bold>
          ║ AI Assistant for Coding ║
        </Text>
        <Text color="cyan" bold>
          ║ ║
        </Text>
        <Text color="cyan" bold>
          ╚═══════════════════════════════════╝
        </Text>
      </Box>

      {/* Current Configuration */}
      <Box flexDirection="column" alignItems="center" marginBottom={2}>
        <Text color="white" bold>
          Current Configuration:
        </Text>
        <Text color="green">Model: {formatModel(config.getModel())}</Text>
        <Text color="green">Workspace: {config.getWorkspaceRoot()}</Text>
        {config.getDebug() && <Text color="yellow">Debug Mode: Enabled</Text>}
      </Box>

      {/* Menu Options */}
      <Box flexDirection="column" alignItems="center" marginBottom={2}>
        <Text color="white" bold>
          What would you like to do?
        </Text>
        <Text></Text>
        <Text color="cyan">[C] Start Chat Session</Text>
        <Text color="yellow">[S] Settings</Text>
        <Text color="green">[V] Service Status</Text>
        <Text color="magenta">[H] Help</Text>
        <Text color="red">[Q] Quit</Text>
      </Box>

      {/* Instructions */}
      <Box flexDirection="column" alignItems="center">
        <Text color="gray" dimColor>
          Press the corresponding key or Enter to start chatting
        </Text>
        <Text color="gray" dimColor>
          Use Ctrl+C to exit at any time
        </Text>
      </Box>

      {/* Version Info */}
      <Box position="absolute" bottom={0} right={0} padding={1}>
        <Text color="gray" dimColor>
          v1.0.0
        </Text>
      </Box>
    </Box>
  );
};
