{"version": 3, "sources": ["../../../src/utils/memoryDiscovery.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport * as fs from 'fs/promises';\nimport * as fsSync from 'fs';\nimport * as path from 'path';\nimport { homedir } from 'os';\nimport { bfsFileSearch } from './bfsFileSearch.js';\nimport {\n  GEMINI_CONFIG_DIR,\n  getAllGeminiMdFilenames,\n} from '../tools/memory.js';\nimport { FileDiscoveryService } from '../services/fileDiscoveryService.js';\nimport { processImports } from './memoryImportProcessor.js';\n\n// Simple console logger, similar to the one previously in CLI's config.ts\n// TODO: Integrate with a more robust server-side logger if available/appropriate.\nconst logger = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  debug: (...args: any[]) =>\n    console.debug('[DEBUG] [MemoryDiscovery]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  warn: (...args: any[]) => console.warn('[WARN] [MemoryDiscovery]', ...args),\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  error: (...args: any[]) =>\n    console.error('[ERROR] [MemoryDiscovery]', ...args),\n};\n\nconst MAX_DIRECTORIES_TO_SCAN_FOR_MEMORY = 200;\n\ninterface GeminiFileContent {\n  filePath: string;\n  content: string | null;\n}\n\nasync function findProjectRoot(startDir: string): Promise<string | null> {\n  let currentDir = path.resolve(startDir);\n  while (true) {\n    const gitPath = path.join(currentDir, '.git');\n    try {\n      const stats = await fs.stat(gitPath);\n      if (stats.isDirectory()) {\n        return currentDir;\n      }\n    } catch (error: unknown) {\n      // Don't log ENOENT errors as they're expected when .git doesn't exist\n      // Also don't log errors in test environments, which often have mocked fs\n      const isENOENT =\n        typeof error === 'object' &&\n        error !== null &&\n        'code' in error &&\n        (error as { code: string }).code === 'ENOENT';\n\n      // Only log unexpected errors in non-test environments\n      // process.env.NODE_ENV === 'test' or VITEST are common test indicators\n      const isTestEnv = process.env.NODE_ENV === 'test' || process.env.VITEST;\n\n      if (!isENOENT && !isTestEnv) {\n        if (typeof error === 'object' && error !== null && 'code' in error) {\n          const fsError = error as { code: string; message: string };\n          logger.warn(\n            `Error checking for .git directory at ${gitPath}: ${fsError.message}`,\n          );\n        } else {\n          logger.warn(\n            `Non-standard error checking for .git directory at ${gitPath}: ${String(error)}`,\n          );\n        }\n      }\n    }\n    const parentDir = path.dirname(currentDir);\n    if (parentDir === currentDir) {\n      return null;\n    }\n    currentDir = parentDir;\n  }\n}\n\nasync function getGeminiMdFilePathsInternal(\n  currentWorkingDirectory: string,\n  userHomePath: string,\n  debugMode: boolean,\n  fileService: FileDiscoveryService,\n  extensionContextFilePaths: string[] = [],\n): Promise<string[]> {\n  const allPaths = new Set<string>();\n  const geminiMdFilenames = getAllGeminiMdFilenames();\n\n  for (const geminiMdFilename of geminiMdFilenames) {\n    const resolvedCwd = path.resolve(currentWorkingDirectory);\n    const resolvedHome = path.resolve(userHomePath);\n    const globalMemoryPath = path.join(\n      resolvedHome,\n      GEMINI_CONFIG_DIR,\n      geminiMdFilename,\n    );\n\n    if (debugMode)\n      logger.debug(\n        `Searching for ${geminiMdFilename} starting from CWD: ${resolvedCwd}`,\n      );\n    if (debugMode) logger.debug(`User home directory: ${resolvedHome}`);\n\n    try {\n      await fs.access(globalMemoryPath, fsSync.constants.R_OK);\n      allPaths.add(globalMemoryPath);\n      if (debugMode)\n        logger.debug(\n          `Found readable global ${geminiMdFilename}: ${globalMemoryPath}`,\n        );\n    } catch {\n      if (debugMode)\n        logger.debug(\n          `Global ${geminiMdFilename} not found or not readable: ${globalMemoryPath}`,\n        );\n    }\n\n    const projectRoot = await findProjectRoot(resolvedCwd);\n    if (debugMode)\n      logger.debug(`Determined project root: ${projectRoot ?? 'None'}`);\n\n    const upwardPaths: string[] = [];\n    let currentDir = resolvedCwd;\n    // Determine the directory that signifies the top of the project or user-specific space.\n    const ultimateStopDir = projectRoot\n      ? path.dirname(projectRoot)\n      : path.dirname(resolvedHome);\n\n    while (currentDir && currentDir !== path.dirname(currentDir)) {\n      // Loop until filesystem root or currentDir is empty\n      if (debugMode) {\n        logger.debug(\n          `Checking for ${geminiMdFilename} in (upward scan): ${currentDir}`,\n        );\n      }\n\n      // Skip the global .gemini directory itself during upward scan from CWD,\n      // as global is handled separately and explicitly first.\n      if (currentDir === path.join(resolvedHome, GEMINI_CONFIG_DIR)) {\n        if (debugMode) {\n          logger.debug(\n            `Upward scan reached global config dir path, stopping upward search here: ${currentDir}`,\n          );\n        }\n        break;\n      }\n\n      const potentialPath = path.join(currentDir, geminiMdFilename);\n      try {\n        await fs.access(potentialPath, fsSync.constants.R_OK);\n        // Add to upwardPaths only if it's not the already added globalMemoryPath\n        if (potentialPath !== globalMemoryPath) {\n          upwardPaths.unshift(potentialPath);\n          if (debugMode) {\n            logger.debug(\n              `Found readable upward ${geminiMdFilename}: ${potentialPath}`,\n            );\n          }\n        }\n      } catch {\n        if (debugMode) {\n          logger.debug(\n            `Upward ${geminiMdFilename} not found or not readable in: ${currentDir}`,\n          );\n        }\n      }\n\n      // Stop condition: if currentDir is the ultimateStopDir, break after this iteration.\n      if (currentDir === ultimateStopDir) {\n        if (debugMode)\n          logger.debug(\n            `Reached ultimate stop directory for upward scan: ${currentDir}`,\n          );\n        break;\n      }\n\n      currentDir = path.dirname(currentDir);\n    }\n    upwardPaths.forEach((p) => allPaths.add(p));\n\n    const downwardPaths = await bfsFileSearch(resolvedCwd, {\n      fileName: geminiMdFilename,\n      maxDirs: MAX_DIRECTORIES_TO_SCAN_FOR_MEMORY,\n      debug: debugMode,\n      fileService,\n    });\n    downwardPaths.sort(); // Sort for consistent ordering, though hierarchy might be more complex\n    if (debugMode && downwardPaths.length > 0)\n      logger.debug(\n        `Found downward ${geminiMdFilename} files (sorted): ${JSON.stringify(\n          downwardPaths,\n        )}`,\n      );\n    // Add downward paths only if they haven't been included already (e.g. from upward scan)\n    for (const dPath of downwardPaths) {\n      allPaths.add(dPath);\n    }\n  }\n\n  // Add extension context file paths\n  for (const extensionPath of extensionContextFilePaths) {\n    allPaths.add(extensionPath);\n  }\n\n  const finalPaths = Array.from(allPaths);\n\n  if (debugMode)\n    logger.debug(\n      `Final ordered ${getAllGeminiMdFilenames()} paths to read: ${JSON.stringify(\n        finalPaths,\n      )}`,\n    );\n  return finalPaths;\n}\n\nasync function readGeminiMdFiles(\n  filePaths: string[],\n  debugMode: boolean,\n): Promise<GeminiFileContent[]> {\n  const results: GeminiFileContent[] = [];\n  for (const filePath of filePaths) {\n    try {\n      const content = await fs.readFile(filePath, 'utf-8');\n\n      // Process imports in the content\n      const processedContent = await processImports(\n        content,\n        path.dirname(filePath),\n        debugMode,\n      );\n\n      results.push({ filePath, content: processedContent });\n      if (debugMode)\n        logger.debug(\n          `Successfully read and processed imports: ${filePath} (Length: ${processedContent.length})`,\n        );\n    } catch (error: unknown) {\n      const isTestEnv = process.env.NODE_ENV === 'test' || process.env.VITEST;\n      if (!isTestEnv) {\n        const message = error instanceof Error ? error.message : String(error);\n        logger.warn(\n          `Warning: Could not read ${getAllGeminiMdFilenames()} file at ${filePath}. Error: ${message}`,\n        );\n      }\n      results.push({ filePath, content: null }); // Still include it with null content\n      if (debugMode) logger.debug(`Failed to read: ${filePath}`);\n    }\n  }\n  return results;\n}\n\nfunction concatenateInstructions(\n  instructionContents: GeminiFileContent[],\n  // CWD is needed to resolve relative paths for display markers\n  currentWorkingDirectoryForDisplay: string,\n): string {\n  return instructionContents\n    .filter((item) => typeof item.content === 'string')\n    .map((item) => {\n      const trimmedContent = (item.content as string).trim();\n      if (trimmedContent.length === 0) {\n        return null;\n      }\n      const displayPath = path.isAbsolute(item.filePath)\n        ? path.relative(currentWorkingDirectoryForDisplay, item.filePath)\n        : item.filePath;\n      return `--- Context from: ${displayPath} ---\\n${trimmedContent}\\n--- End of Context from: ${displayPath} ---`;\n    })\n    .filter((block): block is string => block !== null)\n    .join('\\n\\n');\n}\n\n/**\n * Loads hierarchical GEMINI.md files and concatenates their content.\n * This function is intended for use by the server.\n */\nexport async function loadServerHierarchicalMemory(\n  currentWorkingDirectory: string,\n  debugMode: boolean,\n  fileService: FileDiscoveryService,\n  extensionContextFilePaths: string[] = [],\n): Promise<{ memoryContent: string; fileCount: number }> {\n  if (debugMode)\n    logger.debug(\n      `Loading server hierarchical memory for CWD: ${currentWorkingDirectory}`,\n    );\n  // For the server, homedir() refers to the server process's home.\n  // This is consistent with how MemoryTool already finds the global path.\n  const userHomePath = homedir();\n  const filePaths = await getGeminiMdFilePathsInternal(\n    currentWorkingDirectory,\n    userHomePath,\n    debugMode,\n    fileService,\n    extensionContextFilePaths,\n  );\n  if (filePaths.length === 0) {\n    if (debugMode) logger.debug('No GEMINI.md files found in hierarchy.');\n    return { memoryContent: '', fileCount: 0 };\n  }\n  const contentsWithPaths = await readGeminiMdFiles(filePaths, debugMode);\n  // Pass CWD for relative path display in concatenated content\n  const combinedInstructions = concatenateInstructions(\n    contentsWithPaths,\n    currentWorkingDirectory,\n  );\n  if (debugMode)\n    logger.debug(\n      `Combined instructions length: ${combinedInstructions.length}`,\n    );\n  if (debugMode && combinedInstructions.length > 0)\n    logger.debug(\n      `Combined instructions (snippet): ${combinedInstructions.substring(0, 500)}...`,\n    );\n  return { memoryContent: combinedInstructions, fileCount: filePaths.length };\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,YAAY,QAAQ;AACpB,YAAY,YAAY;AACxB,YAAY,UAAU;AACtB,SAAS,eAAe;AACxB,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAEP,SAAS,sBAAsB;AAI/B,MAAM,SAAS;AAAA;AAAA,EAEb,OAAO,IAAI,SACT,QAAQ,MAAM,6BAA6B,GAAG,IAAI;AAAA;AAAA,EAEpD,MAAM,IAAI,SAAgB,QAAQ,KAAK,4BAA4B,GAAG,IAAI;AAAA;AAAA,EAE1E,OAAO,IAAI,SACT,QAAQ,MAAM,6BAA6B,GAAG,IAAI;AACtD;AAEA,MAAM,qCAAqC;AAO3C,eAAe,gBAAgB,UAA0C;AACvE,MAAI,aAAa,KAAK,QAAQ,QAAQ;AACtC,SAAO,MAAM;AACX,UAAM,UAAU,KAAK,KAAK,YAAY,MAAM;AAC5C,QAAI;AACF,YAAM,QAAQ,MAAM,GAAG,KAAK,OAAO;AACnC,UAAI,MAAM,YAAY,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,IACF,SAAS,OAAgB;AAGvB,YAAM,WACJ,OAAO,UAAU,YACjB,UAAU,QACV,UAAU,SACT,MAA2B,SAAS;AAIvC,YAAM,YAAY,QAAQ,IAAI,aAAa,UAAU,QAAQ,IAAI;AAEjE,UAAI,CAAC,YAAY,CAAC,WAAW;AAC3B,YAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,UAAU,OAAO;AAClE,gBAAM,UAAU;AAChB,iBAAO;AAAA,YACL,wCAAwC,OAAO,KAAK,QAAQ,OAAO;AAAA,UACrE;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,YACL,qDAAqD,OAAO,KAAK,OAAO,KAAK,CAAC;AAAA,UAChF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,YAAY,KAAK,QAAQ,UAAU;AACzC,QAAI,cAAc,YAAY;AAC5B,aAAO;AAAA,IACT;AACA,iBAAa;AAAA,EACf;AACF;AAEA,eAAe,6BACb,yBACA,cACA,WACA,aACA,4BAAsC,CAAC,GACpB;AACnB,QAAM,WAAW,oBAAI,IAAY;AACjC,QAAM,oBAAoB,wBAAwB;AAElD,aAAW,oBAAoB,mBAAmB;AAChD,UAAM,cAAc,KAAK,QAAQ,uBAAuB;AACxD,UAAM,eAAe,KAAK,QAAQ,YAAY;AAC9C,UAAM,mBAAmB,KAAK;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI;AACF,aAAO;AAAA,QACL,iBAAiB,gBAAgB,uBAAuB,WAAW;AAAA,MACrE;AACF,QAAI,UAAW,QAAO,MAAM,wBAAwB,YAAY,EAAE;AAElE,QAAI;AACF,YAAM,GAAG,OAAO,kBAAkB,OAAO,UAAU,IAAI;AACvD,eAAS,IAAI,gBAAgB;AAC7B,UAAI;AACF,eAAO;AAAA,UACL,yBAAyB,gBAAgB,KAAK,gBAAgB;AAAA,QAChE;AAAA,IACJ,QAAQ;AACN,UAAI;AACF,eAAO;AAAA,UACL,UAAU,gBAAgB,+BAA+B,gBAAgB;AAAA,QAC3E;AAAA,IACJ;AAEA,UAAM,cAAc,MAAM,gBAAgB,WAAW;AACrD,QAAI;AACF,aAAO,MAAM,4BAA4B,eAAe,MAAM,EAAE;AAElE,UAAM,cAAwB,CAAC;AAC/B,QAAI,aAAa;AAEjB,UAAM,kBAAkB,cACpB,KAAK,QAAQ,WAAW,IACxB,KAAK,QAAQ,YAAY;AAE7B,WAAO,cAAc,eAAe,KAAK,QAAQ,UAAU,GAAG;AAE5D,UAAI,WAAW;AACb,eAAO;AAAA,UACL,gBAAgB,gBAAgB,sBAAsB,UAAU;AAAA,QAClE;AAAA,MACF;AAIA,UAAI,eAAe,KAAK,KAAK,cAAc,iBAAiB,GAAG;AAC7D,YAAI,WAAW;AACb,iBAAO;AAAA,YACL,4EAA4E,UAAU;AAAA,UACxF;AAAA,QACF;AACA;AAAA,MACF;AAEA,YAAM,gBAAgB,KAAK,KAAK,YAAY,gBAAgB;AAC5D,UAAI;AACF,cAAM,GAAG,OAAO,eAAe,OAAO,UAAU,IAAI;AAEpD,YAAI,kBAAkB,kBAAkB;AACtC,sBAAY,QAAQ,aAAa;AACjC,cAAI,WAAW;AACb,mBAAO;AAAA,cACL,yBAAyB,gBAAgB,KAAK,aAAa;AAAA,YAC7D;AAAA,UACF;AAAA,QACF;AAAA,MACF,QAAQ;AACN,YAAI,WAAW;AACb,iBAAO;AAAA,YACL,UAAU,gBAAgB,kCAAkC,UAAU;AAAA,UACxE;AAAA,QACF;AAAA,MACF;AAGA,UAAI,eAAe,iBAAiB;AAClC,YAAI;AACF,iBAAO;AAAA,YACL,oDAAoD,UAAU;AAAA,UAChE;AACF;AAAA,MACF;AAEA,mBAAa,KAAK,QAAQ,UAAU;AAAA,IACtC;AACA,gBAAY,QAAQ,CAAC,MAAM,SAAS,IAAI,CAAC,CAAC;AAE1C,UAAM,gBAAgB,MAAM,cAAc,aAAa;AAAA,MACrD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,OAAO;AAAA,MACP;AAAA,IACF,CAAC;AACD,kBAAc,KAAK;AACnB,QAAI,aAAa,cAAc,SAAS;AACtC,aAAO;AAAA,QACL,kBAAkB,gBAAgB,oBAAoB,KAAK;AAAA,UACzD;AAAA,QACF,CAAC;AAAA,MACH;AAEF,eAAW,SAAS,eAAe;AACjC,eAAS,IAAI,KAAK;AAAA,IACpB;AAAA,EACF;AAGA,aAAW,iBAAiB,2BAA2B;AACrD,aAAS,IAAI,aAAa;AAAA,EAC5B;AAEA,QAAM,aAAa,MAAM,KAAK,QAAQ;AAEtC,MAAI;AACF,WAAO;AAAA,MACL,iBAAiB,wBAAwB,CAAC,mBAAmB,KAAK;AAAA,QAChE;AAAA,MACF,CAAC;AAAA,IACH;AACF,SAAO;AACT;AAEA,eAAe,kBACb,WACA,WAC8B;AAC9B,QAAM,UAA+B,CAAC;AACtC,aAAW,YAAY,WAAW;AAChC,QAAI;AACF,YAAM,UAAU,MAAM,GAAG,SAAS,UAAU,OAAO;AAGnD,YAAM,mBAAmB,MAAM;AAAA,QAC7B;AAAA,QACA,KAAK,QAAQ,QAAQ;AAAA,QACrB;AAAA,MACF;AAEA,cAAQ,KAAK,EAAE,UAAU,SAAS,iBAAiB,CAAC;AACpD,UAAI;AACF,eAAO;AAAA,UACL,4CAA4C,QAAQ,aAAa,iBAAiB,MAAM;AAAA,QAC1F;AAAA,IACJ,SAAS,OAAgB;AACvB,YAAM,YAAY,QAAQ,IAAI,aAAa,UAAU,QAAQ,IAAI;AACjE,UAAI,CAAC,WAAW;AACd,cAAM,UAAU,iBAAiB,QAAQ,MAAM,UAAU,OAAO,KAAK;AACrE,eAAO;AAAA,UACL,2BAA2B,wBAAwB,CAAC,YAAY,QAAQ,YAAY,OAAO;AAAA,QAC7F;AAAA,MACF;AACA,cAAQ,KAAK,EAAE,UAAU,SAAS,KAAK,CAAC;AACxC,UAAI,UAAW,QAAO,MAAM,mBAAmB,QAAQ,EAAE;AAAA,IAC3D;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,wBACP,qBAEA,mCACQ;AACR,SAAO,oBACJ,OAAO,CAAC,SAAS,OAAO,KAAK,YAAY,QAAQ,EACjD,IAAI,CAAC,SAAS;AACb,UAAM,iBAAkB,KAAK,QAAmB,KAAK;AACrD,QAAI,eAAe,WAAW,GAAG;AAC/B,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,IAC7C,KAAK,SAAS,mCAAmC,KAAK,QAAQ,IAC9D,KAAK;AACT,WAAO,qBAAqB,WAAW;AAAA,EAAS,cAAc;AAAA,2BAA8B,WAAW;AAAA,EACzG,CAAC,EACA,OAAO,CAAC,UAA2B,UAAU,IAAI,EACjD,KAAK,MAAM;AAChB;AAMA,eAAsB,6BACpB,yBACA,WACA,aACA,4BAAsC,CAAC,GACgB;AACvD,MAAI;AACF,WAAO;AAAA,MACL,+CAA+C,uBAAuB;AAAA,IACxE;AAGF,QAAM,eAAe,QAAQ;AAC7B,QAAM,YAAY,MAAM;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,UAAU,WAAW,GAAG;AAC1B,QAAI,UAAW,QAAO,MAAM,wCAAwC;AACpE,WAAO,EAAE,eAAe,IAAI,WAAW,EAAE;AAAA,EAC3C;AACA,QAAM,oBAAoB,MAAM,kBAAkB,WAAW,SAAS;AAEtE,QAAM,uBAAuB;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACA,MAAI;AACF,WAAO;AAAA,MACL,iCAAiC,qBAAqB,MAAM;AAAA,IAC9D;AACF,MAAI,aAAa,qBAAqB,SAAS;AAC7C,WAAO;AAAA,MACL,oCAAoC,qBAAqB,UAAU,GAAG,GAAG,CAAC;AAAA,IAC5E;AACF,SAAO,EAAE,eAAe,sBAAsB,WAAW,UAAU,OAAO;AAC5E;", "names": []}