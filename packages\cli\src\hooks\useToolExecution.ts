/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { useState, useCallback, useRef } from 'react';
import { 
  Config, 
  ToolApprovalRequest, 
  ToolApproval,
  ToolParams,
  ToolResult,
  ToolExecutionContext,
  ApprovalMode
} from '@arien/arien-cli-core';

export interface UseToolExecutionReturn {
  pendingApproval: ToolApprovalRequest | null;
  approveTool: () => void;
  denyTool: (reason?: string) => void;
  approveAllTools: () => void;
  denyAllTools: () => void;
  getApprovalCallback: () => (request: ToolApprovalRequest) => Promise<ToolApproval>;
}

export const useToolExecution = (config: Config): UseToolExecutionReturn => {
  const [pendingApproval, setPendingApproval] = useState<ToolApprovalRequest | null>(null);
  const [approvalMode, setApprovalMode] = useState<ApprovalMode>(config.getApprovalMode());
  
  // Store the resolve function for the current approval promise
  const approvalResolveRef = useRef<((approval: ToolApproval) => void) | null>(null);
  
  // Track approved/denied tools for "once" mode
  const approvedToolsRef = useRef<Set<string>>(new Set());
  const deniedToolsRef = useRef<Set<string>>(new Set());

  const approveTool = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      // Remember approval for "once" mode
      if (approvalMode === ApprovalMode.ONCE) {
        approvedToolsRef.current.add(pendingApproval.toolName);
      }
      
      approvalResolveRef.current({
        approved: true,
        rememberChoice: approvalMode === ApprovalMode.ONCE,
      });
      
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, approvalMode]);

  const denyTool = useCallback((reason?: string) => {
    if (pendingApproval && approvalResolveRef.current) {
      // Remember denial for "once" mode
      if (approvalMode === ApprovalMode.ONCE) {
        deniedToolsRef.current.add(pendingApproval.toolName);
      }
      
      approvalResolveRef.current({
        approved: false,
        reason: reason || 'Tool execution denied by user',
        rememberChoice: approvalMode === ApprovalMode.ONCE,
      });
      
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, approvalMode]);

  const approveAllTools = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      // Set approval mode to always
      setApprovalMode(ApprovalMode.ALWAYS);
      config.setApprovalMode(ApprovalMode.ALWAYS);
      
      approvalResolveRef.current({
        approved: true,
        rememberChoice: true,
      });
      
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, config]);

  const denyAllTools = useCallback(() => {
    if (pendingApproval && approvalResolveRef.current) {
      // Set approval mode to never
      setApprovalMode(ApprovalMode.NEVER);
      config.setApprovalMode(ApprovalMode.NEVER);
      
      approvalResolveRef.current({
        approved: false,
        reason: 'All tool execution denied by user',
        rememberChoice: true,
      });
      
      setPendingApproval(null);
      approvalResolveRef.current = null;
    }
  }, [pendingApproval, config]);

  const getApprovalCallback = useCallback(() => {
    return async (request: ToolApprovalRequest): Promise<ToolApproval> => {
      // Check approval mode
      const currentMode = config.getApprovalMode();
      
      // Always approve
      if (currentMode === ApprovalMode.ALWAYS) {
        return {
          approved: true,
          rememberChoice: true,
        };
      }
      
      // Never approve
      if (currentMode === ApprovalMode.NEVER) {
        return {
          approved: false,
          reason: 'Tool execution disabled by approval mode',
          rememberChoice: true,
        };
      }
      
      // Once mode - check if we've already decided on this tool
      if (currentMode === ApprovalMode.ONCE) {
        if (approvedToolsRef.current.has(request.toolName)) {
          return {
            approved: true,
            rememberChoice: true,
          };
        }
        
        if (deniedToolsRef.current.has(request.toolName)) {
          return {
            approved: false,
            reason: 'Tool execution previously denied',
            rememberChoice: true,
          };
        }
      }
      
      // Need user approval - show UI and wait for response
      return new Promise<ToolApproval>((resolve) => {
        setPendingApproval(request);
        approvalResolveRef.current = resolve;
      });
    };
  }, [config]);

  return {
    pendingApproval,
    approveTool,
    denyTool,
    approveAllTools,
    denyAllTools,
    getApprovalCallback,
  };
};
