{"version": 3, "sources": ["../../../src/utils/errors.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nexport class ArienError extends Error {\n  public readonly code: string;\n  public readonly context?: Record<string, any>;\n\n  constructor(\n    message: string,\n    code: string = 'UNKNOWN_ERROR',\n    context?: Record<string, any>,\n  ) {\n    super(message);\n    this.name = 'ArienError';\n    this.code = code;\n    this.context = context;\n  }\n}\n\nexport class ConfigurationError extends ArienError {\n  constructor(message: string, context?: Record<string, any>) {\n    super(message, 'CONFIGURATION_ERROR', context);\n    this.name = 'ConfigurationError';\n  }\n}\n\nexport class AuthenticationError extends ArienError {\n  constructor(message: string, context?: Record<string, any>) {\n    super(message, 'AUTHENTICATION_ERROR', context);\n    this.name = 'AuthenticationError';\n  }\n}\n\nexport class ToolExecutionError extends ArienError {\n  public readonly toolName: string;\n\n  constructor(\n    message: string,\n    toolName: string,\n    context?: Record<string, any>,\n  ) {\n    super(message, 'TOOL_EXECUTION_ERROR', context);\n    this.name = 'ToolExecutionError';\n    this.toolName = toolName;\n  }\n}\n\nexport class FileSystemError extends ArienError {\n  public readonly path: string;\n  public readonly operation: string;\n\n  constructor(\n    message: string,\n    path: string,\n    operation: string,\n    context?: Record<string, any>,\n  ) {\n    super(message, 'FILESYSTEM_ERROR', context);\n    this.name = 'FileSystemError';\n    this.path = path;\n    this.operation = operation;\n  }\n}\n\nexport class NetworkError extends ArienError {\n  public readonly url?: string;\n  public readonly statusCode?: number;\n\n  constructor(\n    message: string,\n    url?: string,\n    statusCode?: number,\n    context?: Record<string, any>,\n  ) {\n    super(message, 'NETWORK_ERROR', context);\n    this.name = 'NetworkError';\n    this.url = url;\n    this.statusCode = statusCode;\n  }\n}\n\nexport class ValidationError extends ArienError {\n  public readonly field?: string;\n\n  constructor(message: string, field?: string, context?: Record<string, any>) {\n    super(message, 'VALIDATION_ERROR', context);\n    this.name = 'ValidationError';\n    this.field = field;\n  }\n}\n\nexport class RateLimitError extends ArienError {\n  public readonly retryAfter?: number;\n\n  constructor(\n    message: string,\n    retryAfter?: number,\n    context?: Record<string, any>,\n  ) {\n    super(message, 'RATE_LIMIT_ERROR', context);\n    this.name = 'RateLimitError';\n    this.retryAfter = retryAfter;\n  }\n}\n\nexport class TimeoutError extends ArienError {\n  public readonly timeout: number;\n\n  constructor(message: string, timeout: number, context?: Record<string, any>) {\n    super(message, 'TIMEOUT_ERROR', context);\n    this.name = 'TimeoutError';\n    this.timeout = timeout;\n  }\n}\n\n// Error handling utilities\nexport function isArienError(error: unknown): error is ArienError {\n  return error instanceof ArienError;\n}\n\nexport function isNodeError(error: unknown): error is NodeJS.ErrnoException {\n  return error instanceof Error && 'code' in error && 'errno' in error;\n}\n\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return String(error);\n}\n\nexport function getErrorCode(error: unknown): string {\n  if (isArienError(error)) {\n    return error.code;\n  }\n  if (error instanceof Error) {\n    return error.name;\n  }\n  return 'UNKNOWN_ERROR';\n}\n\nexport function formatError(error: unknown): string {\n  if (isArienError(error)) {\n    let formatted = `[${error.code}] ${error.message}`;\n    if (error.context) {\n      formatted += ` (Context: ${JSON.stringify(error.context)})`;\n    }\n    return formatted;\n  }\n\n  if (error instanceof Error) {\n    return `[${error.name}] ${error.message}`;\n  }\n\n  return `[UNKNOWN_ERROR] ${String(error)}`;\n}\n\nexport function createErrorContext(\n  additionalContext?: Record<string, any>,\n): Record<string, any> {\n  return {\n    timestamp: new Date().toISOString(),\n    nodeVersion: process.version,\n    platform: process.platform,\n    arch: process.arch,\n    ...additionalContext,\n  };\n}\n\n// Error recovery utilities\nexport interface RetryOptions {\n  maxAttempts: number;\n  baseDelay: number;\n  maxDelay: number;\n  backoffFactor: number;\n  retryCondition?: (error: unknown) => boolean;\n}\n\nexport async function withRetry<T>(\n  operation: () => Promise<T>,\n  options: Partial<RetryOptions> = {},\n): Promise<T> {\n  const {\n    maxAttempts = 3,\n    baseDelay = 1000,\n    maxDelay = 10000,\n    backoffFactor = 2,\n    retryCondition = () => true,\n  } = options;\n\n  let lastError: unknown;\n\n  for (let attempt = 1; attempt <= maxAttempts; attempt++) {\n    try {\n      return await operation();\n    } catch (error) {\n      lastError = error;\n\n      if (attempt === maxAttempts || !retryCondition(error)) {\n        throw error;\n      }\n\n      const delay = Math.min(\n        baseDelay * Math.pow(backoffFactor, attempt - 1),\n        maxDelay,\n      );\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n\n  throw lastError;\n}\n\nexport function shouldRetryError(error: unknown): boolean {\n  if (isArienError(error)) {\n    // Don't retry validation or configuration errors\n    return ![\n      'VALIDATION_ERROR',\n      'CONFIGURATION_ERROR',\n      'AUTHENTICATION_ERROR',\n    ].includes(error.code);\n  }\n\n  if (error instanceof Error) {\n    // Don't retry syntax errors or type errors\n    return !['SyntaxError', 'TypeError', 'ReferenceError'].includes(error.name);\n  }\n\n  return true;\n}\n\n// Error reporting utilities\nexport interface ErrorReport {\n  error: string;\n  code: string;\n  timestamp: string;\n  context?: Record<string, any>;\n  stack?: string;\n}\n\nexport function createErrorReport(\n  error: unknown,\n  additionalContext?: Record<string, any>,\n): ErrorReport {\n  const report: ErrorReport = {\n    error: getErrorMessage(error),\n    code: getErrorCode(error),\n    timestamp: new Date().toISOString(),\n  };\n\n  if (error instanceof Error && error.stack) {\n    report.stack = error.stack;\n  }\n\n  if (isArienError(error) && error.context) {\n    report.context = { ...error.context, ...additionalContext };\n  } else if (additionalContext) {\n    report.context = additionalContext;\n  }\n\n  return report;\n}\n\n// Graceful error handling for async operations\nexport async function safeAsync<T>(\n  operation: () => Promise<T>,\n  fallback?: T,\n  onError?: (error: unknown) => void,\n): Promise<T | undefined> {\n  try {\n    return await operation();\n  } catch (error) {\n    if (onError) {\n      onError(error);\n    }\n    return fallback;\n  }\n}\n\nexport function safeSync<T>(\n  operation: () => T,\n  fallback?: T,\n  onError?: (error: unknown) => void,\n): T | undefined {\n  try {\n    return operation();\n  } catch (error) {\n    if (onError) {\n      onError(error);\n    }\n    return fallback;\n  }\n}\n"], "mappings": "AAAA;AAAA;AAAA;AAAA;AAAA;AAMO,MAAM,mBAAmB,MAAM;AAAA,EACpB;AAAA,EACA;AAAA,EAEhB,YACE,SACA,OAAe,iBACf,SACA;AACA,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;AAEO,MAAM,2BAA2B,WAAW;AAAA,EACjD,YAAY,SAAiB,SAA+B;AAC1D,UAAM,SAAS,uBAAuB,OAAO;AAC7C,SAAK,OAAO;AAAA,EACd;AACF;AAEO,MAAM,4BAA4B,WAAW;AAAA,EAClD,YAAY,SAAiB,SAA+B;AAC1D,UAAM,SAAS,wBAAwB,OAAO;AAC9C,SAAK,OAAO;AAAA,EACd;AACF;AAEO,MAAM,2BAA2B,WAAW;AAAA,EACjC;AAAA,EAEhB,YACE,SACA,UACA,SACA;AACA,UAAM,SAAS,wBAAwB,OAAO;AAC9C,SAAK,OAAO;AACZ,SAAK,WAAW;AAAA,EAClB;AACF;AAEO,MAAM,wBAAwB,WAAW;AAAA,EAC9B;AAAA,EACA;AAAA,EAEhB,YACE,SACA,MACA,WACA,SACA;AACA,UAAM,SAAS,oBAAoB,OAAO;AAC1C,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AACF;AAEO,MAAM,qBAAqB,WAAW;AAAA,EAC3B;AAAA,EACA;AAAA,EAEhB,YACE,SACA,KACA,YACA,SACA;AACA,UAAM,SAAS,iBAAiB,OAAO;AACvC,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,aAAa;AAAA,EACpB;AACF;AAEO,MAAM,wBAAwB,WAAW;AAAA,EAC9B;AAAA,EAEhB,YAAY,SAAiB,OAAgB,SAA+B;AAC1E,UAAM,SAAS,oBAAoB,OAAO;AAC1C,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACf;AACF;AAEO,MAAM,uBAAuB,WAAW;AAAA,EAC7B;AAAA,EAEhB,YACE,SACA,YACA,SACA;AACA,UAAM,SAAS,oBAAoB,OAAO;AAC1C,SAAK,OAAO;AACZ,SAAK,aAAa;AAAA,EACpB;AACF;AAEO,MAAM,qBAAqB,WAAW;AAAA,EAC3B;AAAA,EAEhB,YAAY,SAAiB,SAAiB,SAA+B;AAC3E,UAAM,SAAS,iBAAiB,OAAO;AACvC,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;AAGO,SAAS,aAAa,OAAqC;AAChE,SAAO,iBAAiB;AAC1B;AAEO,SAAS,YAAY,OAAgD;AAC1E,SAAO,iBAAiB,SAAS,UAAU,SAAS,WAAW;AACjE;AAEO,SAAS,gBAAgB,OAAwB;AACtD,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AACA,SAAO,OAAO,KAAK;AACrB;AAEO,SAAS,aAAa,OAAwB;AACnD,MAAI,aAAa,KAAK,GAAG;AACvB,WAAO,MAAM;AAAA,EACf;AACA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;AAEO,SAAS,YAAY,OAAwB;AAClD,MAAI,aAAa,KAAK,GAAG;AACvB,QAAI,YAAY,IAAI,MAAM,IAAI,KAAK,MAAM,OAAO;AAChD,QAAI,MAAM,SAAS;AACjB,mBAAa,cAAc,KAAK,UAAU,MAAM,OAAO,CAAC;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AAEA,MAAI,iBAAiB,OAAO;AAC1B,WAAO,IAAI,MAAM,IAAI,KAAK,MAAM,OAAO;AAAA,EACzC;AAEA,SAAO,mBAAmB,OAAO,KAAK,CAAC;AACzC;AAEO,SAAS,mBACd,mBACqB;AACrB,SAAO;AAAA,IACL,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IAClC,aAAa,QAAQ;AAAA,IACrB,UAAU,QAAQ;AAAA,IAClB,MAAM,QAAQ;AAAA,IACd,GAAG;AAAA,EACL;AACF;AAWA,eAAsB,UACpB,WACA,UAAiC,CAAC,GACtB;AACZ,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,iBAAiB,MAAM;AAAA,EACzB,IAAI;AAEJ,MAAI;AAEJ,WAAS,UAAU,GAAG,WAAW,aAAa,WAAW;AACvD,QAAI;AACF,aAAO,MAAM,UAAU;AAAA,IACzB,SAAS,OAAO;AACd,kBAAY;AAEZ,UAAI,YAAY,eAAe,CAAC,eAAe,KAAK,GAAG;AACrD,cAAM;AAAA,MACR;AAEA,YAAM,QAAQ,KAAK;AAAA,QACjB,YAAY,KAAK,IAAI,eAAe,UAAU,CAAC;AAAA,QAC/C;AAAA,MACF;AACA,YAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,CAAC;AAAA,IACzD;AAAA,EACF;AAEA,QAAM;AACR;AAEO,SAAS,iBAAiB,OAAyB;AACxD,MAAI,aAAa,KAAK,GAAG;AAEvB,WAAO,CAAC;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,SAAS,MAAM,IAAI;AAAA,EACvB;AAEA,MAAI,iBAAiB,OAAO;AAE1B,WAAO,CAAC,CAAC,eAAe,aAAa,gBAAgB,EAAE,SAAS,MAAM,IAAI;AAAA,EAC5E;AAEA,SAAO;AACT;AAWO,SAAS,kBACd,OACA,mBACa;AACb,QAAM,SAAsB;AAAA,IAC1B,OAAO,gBAAgB,KAAK;AAAA,IAC5B,MAAM,aAAa,KAAK;AAAA,IACxB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,EACpC;AAEA,MAAI,iBAAiB,SAAS,MAAM,OAAO;AACzC,WAAO,QAAQ,MAAM;AAAA,EACvB;AAEA,MAAI,aAAa,KAAK,KAAK,MAAM,SAAS;AACxC,WAAO,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,kBAAkB;AAAA,EAC5D,WAAW,mBAAmB;AAC5B,WAAO,UAAU;AAAA,EACnB;AAEA,SAAO;AACT;AAGA,eAAsB,UACpB,WACA,UACA,SACwB;AACxB,MAAI;AACF,WAAO,MAAM,UAAU;AAAA,EACzB,SAAS,OAAO;AACd,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,SACd,WACA,UACA,SACe;AACf,MAAI;AACF,WAAO,UAAU;AAAA,EACnB,SAAS,OAAO;AACd,QAAI,SAAS;AACX,cAAQ,KAAK;AAAA,IACf;AACA,WAAO;AAAA,EACT;AACF;", "names": []}