/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
class ArienError extends Error {
  code;
  context;
  constructor(message, code = "UNKNOWN_ERROR", context) {
    super(message);
    this.name = "ArienError";
    this.code = code;
    this.context = context;
  }
}
class ConfigurationError extends ArienError {
  constructor(message, context) {
    super(message, "CONFIGURATION_ERROR", context);
    this.name = "ConfigurationError";
  }
}
class AuthenticationError extends ArienError {
  constructor(message, context) {
    super(message, "AUTHENTICATION_ERROR", context);
    this.name = "AuthenticationError";
  }
}
class ToolExecutionError extends ArienError {
  toolName;
  constructor(message, toolName, context) {
    super(message, "TOOL_EXECUTION_ERROR", context);
    this.name = "ToolExecutionError";
    this.toolName = toolName;
  }
}
class FileSystemError extends ArienError {
  path;
  operation;
  constructor(message, path, operation, context) {
    super(message, "FILESYSTEM_ERROR", context);
    this.name = "FileSystemError";
    this.path = path;
    this.operation = operation;
  }
}
class NetworkError extends ArienError {
  url;
  statusCode;
  constructor(message, url, statusCode, context) {
    super(message, "NETWORK_ERROR", context);
    this.name = "NetworkError";
    this.url = url;
    this.statusCode = statusCode;
  }
}
class ValidationError extends ArienError {
  field;
  constructor(message, field, context) {
    super(message, "VALIDATION_ERROR", context);
    this.name = "ValidationError";
    this.field = field;
  }
}
class RateLimitError extends ArienError {
  retryAfter;
  constructor(message, retryAfter, context) {
    super(message, "RATE_LIMIT_ERROR", context);
    this.name = "RateLimitError";
    this.retryAfter = retryAfter;
  }
}
class TimeoutError extends ArienError {
  timeout;
  constructor(message, timeout, context) {
    super(message, "TIMEOUT_ERROR", context);
    this.name = "TimeoutError";
    this.timeout = timeout;
  }
}
function isArienError(error) {
  return error instanceof ArienError;
}
function isNodeError(error) {
  return error instanceof Error && "code" in error && "errno" in error;
}
function getErrorMessage(error) {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}
function getErrorCode(error) {
  if (isArienError(error)) {
    return error.code;
  }
  if (error instanceof Error) {
    return error.name;
  }
  return "UNKNOWN_ERROR";
}
function formatError(error) {
  if (isArienError(error)) {
    let formatted = `[${error.code}] ${error.message}`;
    if (error.context) {
      formatted += ` (Context: ${JSON.stringify(error.context)})`;
    }
    return formatted;
  }
  if (error instanceof Error) {
    return `[${error.name}] ${error.message}`;
  }
  return `[UNKNOWN_ERROR] ${String(error)}`;
}
function createErrorContext(additionalContext) {
  return {
    timestamp: (/* @__PURE__ */ new Date()).toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    ...additionalContext
  };
}
async function withRetry(operation, options = {}) {
  const {
    maxAttempts = 3,
    baseDelay = 1e3,
    maxDelay = 1e4,
    backoffFactor = 2,
    retryCondition = () => true
  } = options;
  let lastError;
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      if (attempt === maxAttempts || !retryCondition(error)) {
        throw error;
      }
      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt - 1),
        maxDelay
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }
  throw lastError;
}
function shouldRetryError(error) {
  if (isArienError(error)) {
    return ![
      "VALIDATION_ERROR",
      "CONFIGURATION_ERROR",
      "AUTHENTICATION_ERROR"
    ].includes(error.code);
  }
  if (error instanceof Error) {
    return !["SyntaxError", "TypeError", "ReferenceError"].includes(error.name);
  }
  return true;
}
function createErrorReport(error, additionalContext) {
  const report = {
    error: getErrorMessage(error),
    code: getErrorCode(error),
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  };
  if (error instanceof Error && error.stack) {
    report.stack = error.stack;
  }
  if (isArienError(error) && error.context) {
    report.context = { ...error.context, ...additionalContext };
  } else if (additionalContext) {
    report.context = additionalContext;
  }
  return report;
}
async function safeAsync(operation, fallback, onError) {
  try {
    return await operation();
  } catch (error) {
    if (onError) {
      onError(error);
    }
    return fallback;
  }
}
function safeSync(operation, fallback, onError) {
  try {
    return operation();
  } catch (error) {
    if (onError) {
      onError(error);
    }
    return fallback;
  }
}
export {
  ArienError,
  AuthenticationError,
  ConfigurationError,
  FileSystemError,
  NetworkError,
  RateLimitError,
  TimeoutError,
  ToolExecutionError,
  ValidationError,
  createErrorContext,
  createErrorReport,
  formatError,
  getErrorCode,
  getErrorMessage,
  isArienError,
  isNodeError,
  safeAsync,
  safeSync,
  shouldRetryError,
  withRetry
};
//# sourceMappingURL=errors.js.map
