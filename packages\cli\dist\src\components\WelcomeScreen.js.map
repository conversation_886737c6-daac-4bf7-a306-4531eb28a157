{"version": 3, "sources": ["../../../src/components/WelcomeScreen.tsx"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Arien LLC\n * License: MIT\n */\n\nimport React from 'react';\nimport { Box, Text } from 'ink';\nimport { Config } from '@arien/arien-cli-core';\n\ninterface WelcomeScreenProps {\n  config: Config;\n  onStartChat: () => void;\n  onShowSettings: () => void;\n  onShowHelp: () => void;\n}\n\nexport const WelcomeScreen: React.FC<WelcomeScreenProps> = ({\n  config,\n  onStartChat,\n  onShowSettings,\n  onShowHelp,\n}) => {\n  const formatModel = (model: string) => {\n    return model\n      .replace('gemini-2.0-flash-exp', 'Gemini 2.0 Flash Experimental')\n      .replace('gemini-1.5-pro', 'Gemini 1.5 Pro')\n      .replace('gemini-1.5-flash', 'Gemini 1.5 Flash');\n  };\n\n  return (\n    <Box\n      flexDirection=\"column\"\n      alignItems=\"center\"\n      justifyContent=\"center\"\n      flexGrow={1}\n    >\n      {/* Logo/Title */}\n      <Box flexDirection=\"column\" alignItems=\"center\" marginBottom={2}>\n        <Text color=\"cyan\" bold>\n          ╔═══════════════════════════════════╗\n        </Text>\n        <Text color=\"cyan\" bold>\n          ║ ║\n        </Text>\n        <Text color=\"cyan\" bold>\n          ║ 🤖 ARIEN ║\n        </Text>\n        <Text color=\"cyan\" bold>\n          ║ ║\n        </Text>\n        <Text color=\"cyan\" bold>\n          ║ AI Assistant for Coding ║\n        </Text>\n        <Text color=\"cyan\" bold>\n          ║ ║\n        </Text>\n        <Text color=\"cyan\" bold>\n          ╚═══════════════════════════════════╝\n        </Text>\n      </Box>\n\n      {/* Current Configuration */}\n      <Box flexDirection=\"column\" alignItems=\"center\" marginBottom={2}>\n        <Text color=\"white\" bold>\n          Current Configuration:\n        </Text>\n        <Text color=\"green\">Model: {formatModel(config.getModel())}</Text>\n        <Text color=\"green\">Workspace: {config.getWorkspaceRoot()}</Text>\n        {config.getDebug() && <Text color=\"yellow\">Debug Mode: Enabled</Text>}\n      </Box>\n\n      {/* Menu Options */}\n      <Box flexDirection=\"column\" alignItems=\"center\" marginBottom={2}>\n        <Text color=\"white\" bold>\n          What would you like to do?\n        </Text>\n        <Text></Text>\n        <Text color=\"cyan\">[C] Start Chat Session</Text>\n        <Text color=\"yellow\">[S] Settings</Text>\n        <Text color=\"green\">[V] Service Status</Text>\n        <Text color=\"magenta\">[H] Help</Text>\n        <Text color=\"red\">[Q] Quit</Text>\n      </Box>\n\n      {/* Instructions */}\n      <Box flexDirection=\"column\" alignItems=\"center\">\n        <Text color=\"gray\" dimColor>\n          Press the corresponding key or Enter to start chatting\n        </Text>\n        <Text color=\"gray\" dimColor>\n          Use Ctrl+C to exit at any time\n        </Text>\n      </Box>\n\n      {/* Version Info */}\n      <Box position=\"absolute\" bottom={0} right={0} padding={1}>\n        <Text color=\"gray\" dimColor>\n          v1.0.0\n        </Text>\n      </Box>\n    </Box>\n  );\n};\n"], "mappings": "AAsCM,SACE,KADF;AAtCN;AAAA;AAAA;AAAA;AAAA;AAOA,SAAS,KAAK,YAAY;AAUnB,MAAM,gBAA8C,CAAC;AAAA,EAC1D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,QAAM,cAAc,CAAC,UAAkB;AACrC,WAAO,MACJ,QAAQ,wBAAwB,+BAA+B,EAC/D,QAAQ,kBAAkB,gBAAgB,EAC1C,QAAQ,oBAAoB,kBAAkB;AAAA,EACnD;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,eAAc;AAAA,MACd,YAAW;AAAA,MACX,gBAAe;AAAA,MACf,UAAU;AAAA,MAGV;AAAA,6BAAC,OAAI,eAAc,UAAS,YAAW,UAAS,cAAc,GAC5D;AAAA,8BAAC,QAAK,OAAM,QAAO,MAAI,MAAC,4OAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2BAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2CAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2BAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,mDAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,2BAExB;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,MAAI,MAAC,4OAExB;AAAA,WACF;AAAA,QAGA,qBAAC,OAAI,eAAc,UAAS,YAAW,UAAS,cAAc,GAC5D;AAAA,8BAAC,QAAK,OAAM,SAAQ,MAAI,MAAC,oCAEzB;AAAA,UACA,qBAAC,QAAK,OAAM,SAAQ;AAAA;AAAA,YAAQ,YAAY,OAAO,SAAS,CAAC;AAAA,aAAE;AAAA,UAC3D,qBAAC,QAAK,OAAM,SAAQ;AAAA;AAAA,YAAY,OAAO,iBAAiB;AAAA,aAAE;AAAA,UACzD,OAAO,SAAS,KAAK,oBAAC,QAAK,OAAM,UAAS,iCAAmB;AAAA,WAChE;AAAA,QAGA,qBAAC,OAAI,eAAc,UAAS,YAAW,UAAS,cAAc,GAC5D;AAAA,8BAAC,QAAK,OAAM,SAAQ,MAAI,MAAC,wCAEzB;AAAA,UACA,oBAAC,QAAK;AAAA,UACN,oBAAC,QAAK,OAAM,QAAO,oCAAsB;AAAA,UACzC,oBAAC,QAAK,OAAM,UAAS,0BAAY;AAAA,UACjC,oBAAC,QAAK,OAAM,SAAQ,gCAAkB;AAAA,UACtC,oBAAC,QAAK,OAAM,WAAU,sBAAQ;AAAA,UAC9B,oBAAC,QAAK,OAAM,OAAM,sBAAQ;AAAA,WAC5B;AAAA,QAGA,qBAAC,OAAI,eAAc,UAAS,YAAW,UACrC;AAAA,8BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,oEAE5B;AAAA,UACA,oBAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,4CAE5B;AAAA,WACF;AAAA,QAGA,oBAAC,OAAI,UAAS,YAAW,QAAQ,GAAG,OAAO,GAAG,SAAS,GACrD,8BAAC,QAAK,OAAM,QAAO,UAAQ,MAAC,oBAE5B,GACF;AAAA;AAAA;AAAA,EACF;AAEJ;", "names": []}